.class public final Lj3/a;
.super Ljava/lang/Object;
.source "ClientMetrics.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lj3/a$a;
    }
.end annotation


# static fields
.field private static final e:Lj3/a;


# instance fields
.field private final a:Lj3/f;

.field private final b:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Lj3/d;",
            ">;"
        }
    .end annotation
.end field

.field private final c:Lj3/b;

.field private final d:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Lj3/a$a;

    .line 2
    .line 3
    invoke-direct {v0}, Lj3/a$a;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0}, Lj3/a$a;->b()Lj3/a;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    sput-object v0, Lj3/a;->e:Lj3/a;

    .line 11
    .line 12
    return-void
.end method

.method constructor <init>(Lj3/f;Ljava/util/List;Lj3/b;Ljava/lang/String;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lj3/f;",
            "Ljava/util/List<",
            "Lj3/d;",
            ">;",
            "Lj3/b;",
            "Ljava/lang/String;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lj3/a;->a:Lj3/f;

    .line 5
    .line 6
    iput-object p2, p0, Lj3/a;->b:Ljava/util/List;

    .line 7
    .line 8
    iput-object p3, p0, Lj3/a;->c:Lj3/b;

    .line 9
    .line 10
    iput-object p4, p0, Lj3/a;->d:Ljava/lang/String;

    .line 11
    .line 12
    return-void
.end method

.method public static e()Lj3/a$a;
    .locals 1

    .line 1
    new-instance v0, Lj3/a$a;

    .line 2
    .line 3
    invoke-direct {v0}, Lj3/a$a;-><init>()V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method


# virtual methods
.method public a()Ljava/lang/String;
    .locals 1
    .annotation build Lk5/d;
        tag = 0x4
    .end annotation

    .line 1
    iget-object v0, p0, Lj3/a;->d:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public b()Lj3/b;
    .locals 1
    .annotation build Lk5/d;
        tag = 0x3
    .end annotation

    .line 1
    iget-object v0, p0, Lj3/a;->c:Lj3/b;

    .line 2
    .line 3
    return-object v0
.end method

.method public c()Ljava/util/List;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljava/util/List<",
            "Lj3/d;",
            ">;"
        }
    .end annotation

    .annotation build Lk5/d;
        tag = 0x2
    .end annotation

    .line 1
    iget-object v0, p0, Lj3/a;->b:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public d()Lj3/f;
    .locals 1
    .annotation build Lk5/d;
        tag = 0x1
    .end annotation

    .line 1
    iget-object v0, p0, Lj3/a;->a:Lj3/f;

    .line 2
    .line 3
    return-object v0
.end method

.method public f()[B
    .locals 1

    .line 1
    invoke-static {p0}, Lf3/l;->a(Ljava/lang/Object;)[B

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
