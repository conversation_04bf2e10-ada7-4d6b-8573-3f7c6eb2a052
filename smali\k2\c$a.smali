.class public Lk2/c$a;
.super Ljava/lang/Object;
.source "NoTransition.java"

# interfaces
.implements Lk2/e;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lk2/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<R:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lk2/e<",
        "TR;>;"
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Lq1/a;Z)Lk2/d;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lq1/a;",
            "Z)",
            "Lk2/d<",
            "TR;>;"
        }
    .end annotation

    .line 1
    sget-object p1, Lk2/c;->a:Lk2/c;

    .line 2
    .line 3
    return-object p1
.end method
