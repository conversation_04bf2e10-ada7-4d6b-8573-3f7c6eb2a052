.class Lj1/f$b;
.super Ljava/lang/Object;
.source "MaterialDialog.java"

# interfaces
.implements Landroid/text/TextWatcher;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lj1/f;->o()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic a:Lj1/f;


# direct methods
.method constructor <init>(Lj1/f;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lj1/f$b;->a:Lj1/f;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public afterTextChanged(Landroid/text/Editable;)V
    .locals 0

    .line 1
    return-void
.end method

.method public beforeTextChanged(Ljava/lang/CharSequence;III)V
    .locals 0

    .line 1
    return-void
.end method

.method public onTextChanged(Ljava/lang/CharSequence;III)V
    .locals 0

    .line 1
    invoke-interface {p1}, Ljava/lang/CharSequence;->toString()Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    invoke-virtual {p1}, Ljava/lang/String;->length()I

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    iget-object p2, p0, Lj1/f$b;->a:Lj1/f;

    .line 10
    .line 11
    iget-object p3, p2, Lj1/f;->c:Lj1/f$d;

    .line 12
    .line 13
    iget-boolean p3, p3, Lj1/f$d;->j0:Z

    .line 14
    .line 15
    const/4 p4, 0x0

    .line 16
    if-nez p3, :cond_1

    .line 17
    .line 18
    if-nez p1, :cond_0

    .line 19
    .line 20
    const/4 p3, 0x1

    .line 21
    const/4 p4, 0x1

    .line 22
    :cond_0
    sget-object p3, Lj1/b;->a:Lj1/b;

    .line 23
    .line 24
    invoke-virtual {p2, p3}, Lj1/f;->e(Lj1/b;)Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 25
    .line 26
    .line 27
    move-result-object p2

    .line 28
    xor-int/lit8 p3, p4, 0x1

    .line 29
    .line 30
    invoke-virtual {p2, p3}, Landroid/view/View;->setEnabled(Z)V

    .line 31
    .line 32
    .line 33
    :cond_1
    iget-object p2, p0, Lj1/f$b;->a:Lj1/f;

    .line 34
    .line 35
    invoke-virtual {p2, p1, p4}, Lj1/f;->k(IZ)V

    .line 36
    .line 37
    .line 38
    iget-object p1, p0, Lj1/f$b;->a:Lj1/f;

    .line 39
    .line 40
    iget-object p1, p1, Lj1/f;->c:Lj1/f$d;

    .line 41
    .line 42
    iget-boolean p2, p1, Lj1/f$d;->l0:Z

    .line 43
    .line 44
    if-nez p2, :cond_2

    .line 45
    .line 46
    return-void

    .line 47
    :cond_2
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 48
    .line 49
    .line 50
    const/4 p1, 0x0

    .line 51
    throw p1
.end method
