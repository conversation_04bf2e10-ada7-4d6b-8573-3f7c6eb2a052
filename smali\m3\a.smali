.class public final synthetic Lm3/a;
.super Ljava/lang/Object;
.source "R8$$SyntheticClass"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field public final synthetic a:Lm3/c;

.field public final synthetic b:Lf3/o;

.field public final synthetic c:Ld3/h;

.field public final synthetic d:Lf3/i;


# direct methods
.method public synthetic constructor <init>(Lm3/c;Lf3/o;Ld3/h;Lf3/i;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lm3/a;->a:Lm3/c;

    .line 5
    .line 6
    iput-object p2, p0, Lm3/a;->b:Lf3/o;

    .line 7
    .line 8
    iput-object p3, p0, Lm3/a;->c:Ld3/h;

    .line 9
    .line 10
    iput-object p4, p0, Lm3/a;->d:Lf3/i;

    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public final run()V
    .locals 4

    .line 1
    iget-object v0, p0, Lm3/a;->a:Lm3/c;

    .line 2
    .line 3
    iget-object v1, p0, Lm3/a;->b:Lf3/o;

    .line 4
    .line 5
    iget-object v2, p0, Lm3/a;->c:Ld3/h;

    .line 6
    .line 7
    iget-object v3, p0, Lm3/a;->d:Lf3/i;

    .line 8
    .line 9
    invoke-static {v0, v1, v2, v3}, Lm3/c;->b(Lm3/c;Lf3/o;Ld3/h;Lf3/i;)V

    .line 10
    .line 11
    .line 12
    return-void
.end method
