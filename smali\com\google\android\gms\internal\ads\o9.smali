.class final Lcom/google/android/gms/internal/ads/o9;
.super Ljava/lang/Object;
.source "com.google.android.gms:play-services-ads@@21.3.0"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field final synthetic a:I

.field final synthetic b:J

.field final synthetic c:Lcom/google/android/gms/internal/ads/zzbbk;


# direct methods
.method constructor <init>(Lcom/google/android/gms/internal/ads/zzbbk;IJ)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/google/android/gms/internal/ads/o9;->c:Lcom/google/android/gms/internal/ads/zzbbk;

    .line 2
    .line 3
    iput p2, p0, Lcom/google/android/gms/internal/ads/o9;->a:I

    .line 4
    .line 5
    iput-wide p3, p0, Lcom/google/android/gms/internal/ads/o9;->b:J

    .line 6
    .line 7
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 8
    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public final run()V
    .locals 4

    .line 1
    iget-object v0, p0, Lcom/google/android/gms/internal/ads/o9;->c:Lcom/google/android/gms/internal/ads/zzbbk;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbbk;->zza(Lcom/google/android/gms/internal/ads/zzbbk;)Lcom/google/android/gms/internal/ads/zzbbl;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget v1, p0, Lcom/google/android/gms/internal/ads/o9;->a:I

    .line 8
    .line 9
    iget-wide v2, p0, Lcom/google/android/gms/internal/ads/o9;->b:J

    .line 10
    .line 11
    invoke-interface {v0, v1, v2, v3}, Lcom/google/android/gms/internal/ads/zzbbl;->zzl(IJ)V

    .line 12
    .line 13
    .line 14
    return-void
.end method
