.class final Lcom/google/android/gms/internal/ads/q10;
.super Ljava/lang/Object;
.source "com.google.android.gms:play-services-ads@@21.3.0"

# interfaces
.implements Lcom/google/android/gms/internal/ads/h10;


# instance fields
.field private final a:Lcom/google/android/gms/internal/ads/c10;


# direct methods
.method constructor <init>(Lcom/google/android/gms/internal/ads/c10;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lcom/google/android/gms/internal/ads/q10;->a:Lcom/google/android/gms/internal/ads/c10;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final zza([B)Lcom/google/android/gms/internal/ads/i10;
    .locals 9
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/security/GeneralSecurityException;
        }
    .end annotation

    .line 1
    invoke-static {}, Lcom/google/android/gms/internal/ads/zzgpa;->zzb()[B

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-static {v0, p1}, Lcom/google/android/gms/internal/ads/zzgpa;->zza([B[B)[B

    .line 6
    .line 7
    .line 8
    move-result-object v3

    .line 9
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzgpa;->zzc([B)[B

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    const/4 v1, 0x2

    .line 14
    new-array v1, v1, [[B

    .line 15
    .line 16
    const/4 v2, 0x0

    .line 17
    aput-object v0, v1, v2

    .line 18
    .line 19
    const/4 v2, 0x1

    .line 20
    aput-object p1, v1, v2

    .line 21
    .line 22
    invoke-static {v1}, Lcom/google/android/gms/internal/ads/zzgnv;->zzc([[B)[B

    .line 23
    .line 24
    .line 25
    move-result-object v5

    .line 26
    sget-object p1, Lcom/google/android/gms/internal/ads/zzgfd;->zzb:[B

    .line 27
    .line 28
    invoke-static {p1}, Lcom/google/android/gms/internal/ads/zzgfd;->zzd([B)[B

    .line 29
    .line 30
    .line 31
    move-result-object v7

    .line 32
    iget-object v1, p0, Lcom/google/android/gms/internal/ads/q10;->a:Lcom/google/android/gms/internal/ads/c10;

    .line 33
    .line 34
    invoke-virtual {v1}, Lcom/google/android/gms/internal/ads/c10;->a()I

    .line 35
    .line 36
    .line 37
    move-result v8

    .line 38
    const/4 v2, 0x0

    .line 39
    const-string v4, "eae_prk"

    .line 40
    .line 41
    const-string v6, "shared_secret"

    .line 42
    .line 43
    invoke-virtual/range {v1 .. v8}, Lcom/google/android/gms/internal/ads/c10;->b([B[BLjava/lang/String;[BLjava/lang/String;[BI)[B

    .line 44
    .line 45
    .line 46
    move-result-object p1

    .line 47
    new-instance v1, Lcom/google/android/gms/internal/ads/i10;

    .line 48
    .line 49
    invoke-direct {v1, p1, v0}, Lcom/google/android/gms/internal/ads/i10;-><init>([B[B)V

    .line 50
    .line 51
    .line 52
    return-object v1
.end method

.method public final zzb()[B
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/security/GeneralSecurityException;
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lcom/google/android/gms/internal/ads/q10;->a:Lcom/google/android/gms/internal/ads/c10;

    .line 2
    .line 3
    invoke-virtual {v0}, Lcom/google/android/gms/internal/ads/c10;->c()[B

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    sget-object v1, Lcom/google/android/gms/internal/ads/zzgfd;->zzf:[B

    .line 8
    .line 9
    invoke-static {v0, v1}, Ljava/util/Arrays;->equals([B[B)Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-eqz v0, :cond_0

    .line 14
    .line 15
    sget-object v0, Lcom/google/android/gms/internal/ads/zzgfd;->zzb:[B

    .line 16
    .line 17
    return-object v0

    .line 18
    :cond_0
    new-instance v0, Ljava/security/GeneralSecurityException;

    .line 19
    .line 20
    const-string v1, "Could not determine HPKE KEM ID"

    .line 21
    .line 22
    invoke-direct {v0, v1}, Ljava/security/GeneralSecurityException;-><init>(Ljava/lang/String;)V

    .line 23
    .line 24
    .line 25
    throw v0
.end method
