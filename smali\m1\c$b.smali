.class final Lm1/c$b;
.super Ljava/lang/Object;
.source "com.android.billingclient:billing-ktx@@5.0.0"

# interfaces
.implements Lm1/g;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lm1/c;->b(Lcom/android/billingclient/api/a;Lcom/android/billingclient/api/f;Lbb/d;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = null
.end annotation


# instance fields
.field final synthetic a:Lub/u;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lub/u<",
            "Lm1/h;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method constructor <init>(Lub/u;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lub/u<",
            "Lm1/h;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lm1/c$b;->a:Lub/u;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(Lcom/android/billingclient/api/d;Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/android/billingclient/api/d;",
            "Ljava/util/List<",
            "Lcom/android/billingclient/api/e;",
            ">;)V"
        }
    .end annotation

    .line 1
    new-instance v0, Lm1/h;

    .line 2
    .line 3
    const-string v1, "billingResult"

    .line 4
    .line 5
    invoke-static {p1, v1}, Lkb/l;->g(Ljava/lang/Object;Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    invoke-direct {v0, p1, p2}, Lm1/h;-><init>(Lcom/android/billingclient/api/d;Ljava/util/List;)V

    .line 9
    .line 10
    .line 11
    iget-object p1, p0, Lm1/c$b;->a:Lub/u;

    .line 12
    .line 13
    invoke-interface {p1, v0}, Lub/u;->i0(Ljava/lang/Object;)Z

    .line 14
    .line 15
    .line 16
    return-void
.end method
