.class final Lm1/c$c;
.super Ljava/lang/Object;
.source "com.android.billingclient:billing-ktx@@5.0.0"

# interfaces
.implements Lm1/i;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lm1/c;->c(Lcom/android/billingclient/api/a;Lm1/l;Lbb/d;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = null
.end annotation


# instance fields
.field final synthetic a:Lub/u;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lub/u<",
            "Lm1/j;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method constructor <init>(Lub/u;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lub/u<",
            "Lm1/j;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lm1/c$c;->a:Lub/u;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(Lcom/android/billingclient/api/d;Ljava/util/List;)V
    .locals 2
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/android/billingclient/api/d;",
            "Ljava/util/List<",
            "Lcom/android/billingclient/api/Purchase;",
            ">;)V"
        }
    .end annotation

    .line 1
    new-instance v0, Lm1/j;

    .line 2
    .line 3
    const-string v1, "billingResult"

    .line 4
    .line 5
    invoke-static {p1, v1}, Lkb/l;->g(Ljava/lang/Object;Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    const-string v1, "purchases"

    .line 9
    .line 10
    invoke-static {p2, v1}, Lkb/l;->g(Ljava/lang/Object;Ljava/lang/String;)V

    .line 11
    .line 12
    .line 13
    invoke-direct {v0, p1, p2}, Lm1/j;-><init>(Lcom/android/billingclient/api/d;Ljava/util/List;)V

    .line 14
    .line 15
    .line 16
    iget-object p1, p0, Lm1/c$c;->a:Lub/u;

    .line 17
    .line 18
    invoke-interface {p1, v0}, Lub/u;->i0(Ljava/lang/Object;)Z

    .line 19
    .line 20
    .line 21
    return-void
.end method
