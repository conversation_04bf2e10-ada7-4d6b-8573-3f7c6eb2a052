.class public interface abstract Lk2/e;
.super Ljava/lang/Object;
.source "TransitionFactory.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<R:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# virtual methods
.method public abstract a(Lq1/a;Z)Lk2/d;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lq1/a;",
            "Z)",
            "Lk2/d<",
            "TR;>;"
        }
    .end annotation
.end method
