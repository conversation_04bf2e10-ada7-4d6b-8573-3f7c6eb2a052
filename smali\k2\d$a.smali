.class public interface abstract Lk2/d$a;
.super Ljava/lang/Object;
.source "Transition.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lk2/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract d(Landroid/graphics/drawable/Drawable;)V
.end method

.method public abstract g()Landroid/graphics/drawable/Drawable;
.end method
