.class public Lj1/f;
.super Lj1/c;
.source "MaterialDialog.java"

# interfaces
.implements Landroid/view/View$OnClickListener;
.implements Lj1/a$c;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lj1/f$d;,
        Lj1/f$e;,
        Lj1/f$g;,
        Lj1/f$f;
    }
.end annotation


# instance fields
.field protected final c:Lj1/f$d;

.field private final d:Landroid/os/Handler;

.field protected e:Landroid/widget/ImageView;

.field protected f:Landroid/widget/TextView;

.field protected g:Landroid/widget/TextView;

.field h:Landroid/widget/EditText;

.field i:Landroidx/recyclerview/widget/RecyclerView;

.field j:Landroid/view/View;

.field k:Landroid/widget/FrameLayout;

.field l:Landroid/widget/ProgressBar;

.field r:Landroid/widget/TextView;

.field s:Landroid/widget/TextView;

.field t:Landroid/widget/TextView;

.field u:Landroid/widget/CheckBox;

.field v:Lcom/afollestad/materialdialogs/internal/MDButton;

.field w:Lcom/afollestad/materialdialogs/internal/MDButton;

.field x:Lcom/afollestad/materialdialogs/internal/MDButton;

.field y:Lj1/f$f;

.field z:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Ljava/lang/Integer;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method protected constructor <init>(Lj1/f$d;)V
    .locals 2
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "InflateParams"
        }
    .end annotation

    .line 1
    iget-object v0, p1, Lj1/f$d;->a:Landroid/content/Context;

    .line 2
    .line 3
    invoke-static {p1}, Lj1/d;->c(Lj1/f$d;)I

    .line 4
    .line 5
    .line 6
    move-result v1

    .line 7
    invoke-direct {p0, v0, v1}, Lj1/c;-><init>(Landroid/content/Context;I)V

    .line 8
    .line 9
    .line 10
    new-instance v0, Landroid/os/Handler;

    .line 11
    .line 12
    invoke-direct {v0}, Landroid/os/Handler;-><init>()V

    .line 13
    .line 14
    .line 15
    iput-object v0, p0, Lj1/f;->d:Landroid/os/Handler;

    .line 16
    .line 17
    iput-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 18
    .line 19
    iget-object v0, p1, Lj1/f$d;->a:Landroid/content/Context;

    .line 20
    .line 21
    invoke-static {v0}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-static {p1}, Lj1/d;->b(Lj1/f$d;)I

    .line 26
    .line 27
    .line 28
    move-result p1

    .line 29
    const/4 v1, 0x0

    .line 30
    invoke-virtual {v0, p1, v1}, Landroid/view/LayoutInflater;->inflate(ILandroid/view/ViewGroup;)Landroid/view/View;

    .line 31
    .line 32
    .line 33
    move-result-object p1

    .line 34
    check-cast p1, Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 35
    .line 36
    iput-object p1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 37
    .line 38
    invoke-static {p0}, Lj1/d;->d(Lj1/f;)V

    .line 39
    .line 40
    .line 41
    return-void
.end method

.method private m()Z
    .locals 1

    .line 1
    iget-object v0, p0, Lj1/f;->c:Lj1/f$d;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 4
    .line 5
    .line 6
    const/4 v0, 0x0

    .line 7
    return v0
.end method

.method private n(Landroid/view/View;)Z
    .locals 0

    .line 1
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 4
    .line 5
    .line 6
    const/4 p1, 0x0

    .line 7
    return p1
.end method


# virtual methods
.method public a(Lj1/f;Landroid/view/View;ILjava/lang/CharSequence;Z)Z
    .locals 3

    .line 1
    invoke-virtual {p2}, Landroid/view/View;->isEnabled()Z

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    const/4 p4, 0x0

    .line 6
    if-nez p1, :cond_0

    .line 7
    .line 8
    return p4

    .line 9
    :cond_0
    iget-object p1, p0, Lj1/f;->y:Lj1/f$f;

    .line 10
    .line 11
    const/4 v0, 0x1

    .line 12
    if-eqz p1, :cond_c

    .line 13
    .line 14
    sget-object v1, Lj1/f$f;->a:Lj1/f$f;

    .line 15
    .line 16
    if-ne p1, v1, :cond_1

    .line 17
    .line 18
    goto/16 :goto_1

    .line 19
    .line 20
    :cond_1
    sget-object p5, Lj1/f$f;->c:Lj1/f$f;

    .line 21
    .line 22
    if-ne p1, p5, :cond_8

    .line 23
    .line 24
    sget p1, Lj1/k;->f:I

    .line 25
    .line 26
    invoke-virtual {p2, p1}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    check-cast p1, Landroid/widget/CheckBox;

    .line 31
    .line 32
    invoke-virtual {p1}, Landroid/view/View;->isEnabled()Z

    .line 33
    .line 34
    .line 35
    move-result p2

    .line 36
    if-nez p2, :cond_2

    .line 37
    .line 38
    return p4

    .line 39
    :cond_2
    iget-object p2, p0, Lj1/f;->z:Ljava/util/List;

    .line 40
    .line 41
    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 42
    .line 43
    .line 44
    move-result-object p5

    .line 45
    invoke-interface {p2, p5}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 46
    .line 47
    .line 48
    move-result p2

    .line 49
    xor-int/2addr p2, v0

    .line 50
    if-eqz p2, :cond_5

    .line 51
    .line 52
    iget-object p2, p0, Lj1/f;->z:Ljava/util/List;

    .line 53
    .line 54
    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 55
    .line 56
    .line 57
    move-result-object p4

    .line 58
    invoke-interface {p2, p4}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 59
    .line 60
    .line 61
    iget-object p2, p0, Lj1/f;->c:Lj1/f$d;

    .line 62
    .line 63
    iget-boolean p2, p2, Lj1/f$d;->D:Z

    .line 64
    .line 65
    if-eqz p2, :cond_4

    .line 66
    .line 67
    invoke-direct {p0}, Lj1/f;->m()Z

    .line 68
    .line 69
    .line 70
    move-result p2

    .line 71
    if-eqz p2, :cond_3

    .line 72
    .line 73
    invoke-virtual {p1, v0}, Landroid/widget/CompoundButton;->setChecked(Z)V

    .line 74
    .line 75
    .line 76
    goto/16 :goto_2

    .line 77
    .line 78
    :cond_3
    iget-object p1, p0, Lj1/f;->z:Ljava/util/List;

    .line 79
    .line 80
    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 81
    .line 82
    .line 83
    move-result-object p2

    .line 84
    invoke-interface {p1, p2}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 85
    .line 86
    .line 87
    goto/16 :goto_2

    .line 88
    .line 89
    :cond_4
    invoke-virtual {p1, v0}, Landroid/widget/CompoundButton;->setChecked(Z)V

    .line 90
    .line 91
    .line 92
    goto/16 :goto_2

    .line 93
    .line 94
    :cond_5
    iget-object p2, p0, Lj1/f;->z:Ljava/util/List;

    .line 95
    .line 96
    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 97
    .line 98
    .line 99
    move-result-object p5

    .line 100
    invoke-interface {p2, p5}, Ljava/util/List;->remove(Ljava/lang/Object;)Z

    .line 101
    .line 102
    .line 103
    iget-object p2, p0, Lj1/f;->c:Lj1/f$d;

    .line 104
    .line 105
    iget-boolean p2, p2, Lj1/f$d;->D:Z

    .line 106
    .line 107
    if-eqz p2, :cond_7

    .line 108
    .line 109
    invoke-direct {p0}, Lj1/f;->m()Z

    .line 110
    .line 111
    .line 112
    move-result p2

    .line 113
    if-eqz p2, :cond_6

    .line 114
    .line 115
    invoke-virtual {p1, p4}, Landroid/widget/CompoundButton;->setChecked(Z)V

    .line 116
    .line 117
    .line 118
    goto/16 :goto_2

    .line 119
    .line 120
    :cond_6
    iget-object p1, p0, Lj1/f;->z:Ljava/util/List;

    .line 121
    .line 122
    invoke-static {p3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 123
    .line 124
    .line 125
    move-result-object p2

    .line 126
    invoke-interface {p1, p2}, Ljava/util/List;->add(Ljava/lang/Object;)Z

    .line 127
    .line 128
    .line 129
    goto/16 :goto_2

    .line 130
    .line 131
    :cond_7
    invoke-virtual {p1, p4}, Landroid/widget/CompoundButton;->setChecked(Z)V

    .line 132
    .line 133
    .line 134
    goto :goto_2

    .line 135
    :cond_8
    sget-object p5, Lj1/f$f;->b:Lj1/f$f;

    .line 136
    .line 137
    if-ne p1, p5, :cond_f

    .line 138
    .line 139
    sget p1, Lj1/k;->f:I

    .line 140
    .line 141
    invoke-virtual {p2, p1}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 142
    .line 143
    .line 144
    move-result-object p1

    .line 145
    check-cast p1, Landroid/widget/RadioButton;

    .line 146
    .line 147
    invoke-virtual {p1}, Landroid/view/View;->isEnabled()Z

    .line 148
    .line 149
    .line 150
    move-result p5

    .line 151
    if-nez p5, :cond_9

    .line 152
    .line 153
    return p4

    .line 154
    :cond_9
    iget-object p5, p0, Lj1/f;->c:Lj1/f$d;

    .line 155
    .line 156
    iget v1, p5, Lj1/f$d;->J:I

    .line 157
    .line 158
    iget-boolean v2, p5, Lj1/f$d;->M:Z

    .line 159
    .line 160
    if-eqz v2, :cond_a

    .line 161
    .line 162
    iget-object v2, p5, Lj1/f$d;->m:Ljava/lang/CharSequence;

    .line 163
    .line 164
    if-nez v2, :cond_a

    .line 165
    .line 166
    invoke-virtual {p0}, Lj1/f;->dismiss()V

    .line 167
    .line 168
    .line 169
    iget-object p5, p0, Lj1/f;->c:Lj1/f$d;

    .line 170
    .line 171
    iput p3, p5, Lj1/f$d;->J:I

    .line 172
    .line 173
    invoke-direct {p0, p2}, Lj1/f;->n(Landroid/view/View;)Z

    .line 174
    .line 175
    .line 176
    goto :goto_0

    .line 177
    :cond_a
    iget-boolean p4, p5, Lj1/f$d;->E:Z

    .line 178
    .line 179
    if-eqz p4, :cond_b

    .line 180
    .line 181
    iput p3, p5, Lj1/f$d;->J:I

    .line 182
    .line 183
    invoke-direct {p0, p2}, Lj1/f;->n(Landroid/view/View;)Z

    .line 184
    .line 185
    .line 186
    move-result p4

    .line 187
    iget-object p2, p0, Lj1/f;->c:Lj1/f$d;

    .line 188
    .line 189
    iput v1, p2, Lj1/f$d;->J:I

    .line 190
    .line 191
    goto :goto_0

    .line 192
    :cond_b
    const/4 p4, 0x1

    .line 193
    :goto_0
    if-eqz p4, :cond_f

    .line 194
    .line 195
    iget-object p2, p0, Lj1/f;->c:Lj1/f$d;

    .line 196
    .line 197
    iput p3, p2, Lj1/f$d;->J:I

    .line 198
    .line 199
    invoke-virtual {p1, v0}, Landroid/widget/CompoundButton;->setChecked(Z)V

    .line 200
    .line 201
    .line 202
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 203
    .line 204
    iget-object p1, p1, Lj1/f$d;->S:Landroidx/recyclerview/widget/RecyclerView$h;

    .line 205
    .line 206
    invoke-virtual {p1, v1}, Landroidx/recyclerview/widget/RecyclerView$h;->y(I)V

    .line 207
    .line 208
    .line 209
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 210
    .line 211
    iget-object p1, p1, Lj1/f$d;->S:Landroidx/recyclerview/widget/RecyclerView$h;

    .line 212
    .line 213
    invoke-virtual {p1, p3}, Landroidx/recyclerview/widget/RecyclerView$h;->y(I)V

    .line 214
    .line 215
    .line 216
    goto :goto_2

    .line 217
    :cond_c
    :goto_1
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 218
    .line 219
    iget-boolean p1, p1, Lj1/f$d;->M:Z

    .line 220
    .line 221
    if-eqz p1, :cond_d

    .line 222
    .line 223
    invoke-virtual {p0}, Lj1/f;->dismiss()V

    .line 224
    .line 225
    .line 226
    :cond_d
    if-nez p5, :cond_e

    .line 227
    .line 228
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 229
    .line 230
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 231
    .line 232
    .line 233
    :cond_e
    if-eqz p5, :cond_f

    .line 234
    .line 235
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 236
    .line 237
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 238
    .line 239
    .line 240
    :cond_f
    :goto_2
    return v0
.end method

.method final d()V
    .locals 2

    .line 1
    iget-object v0, p0, Lj1/f;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    return-void

    .line 6
    :cond_0
    invoke-virtual {v0}, Landroid/view/View;->getViewTreeObserver()Landroid/view/ViewTreeObserver;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    new-instance v1, Lj1/f$a;

    .line 11
    .line 12
    invoke-direct {v1, p0}, Lj1/f$a;-><init>(Lj1/f;)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {v0, v1}, Landroid/view/ViewTreeObserver;->addOnGlobalLayoutListener(Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;)V

    .line 16
    .line 17
    .line 18
    return-void
.end method

.method public dismiss()V
    .locals 1

    .line 1
    iget-object v0, p0, Lj1/f;->h:Landroid/widget/EditText;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Lj1/f;->c:Lj1/f$d;

    .line 6
    .line 7
    invoke-static {p0, v0}, Ll1/a;->f(Landroid/content/DialogInterface;Lj1/f$d;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    invoke-super {p0}, Landroid/app/Dialog;->dismiss()V

    .line 11
    .line 12
    .line 13
    return-void
.end method

.method public final e(Lj1/b;)Lcom/afollestad/materialdialogs/internal/MDButton;
    .locals 1

    .line 1
    sget-object v0, Lj1/f$c;->a:[I

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    aget p1, v0, p1

    .line 8
    .line 9
    const/4 v0, 0x1

    .line 10
    if-eq p1, v0, :cond_1

    .line 11
    .line 12
    const/4 v0, 0x2

    .line 13
    if-eq p1, v0, :cond_0

    .line 14
    .line 15
    iget-object p1, p0, Lj1/f;->v:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 16
    .line 17
    return-object p1

    .line 18
    :cond_0
    iget-object p1, p0, Lj1/f;->x:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 19
    .line 20
    return-object p1

    .line 21
    :cond_1
    iget-object p1, p0, Lj1/f;->w:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 22
    .line 23
    return-object p1
.end method

.method public final f()Lj1/f$d;
    .locals 1

    .line 1
    iget-object v0, p0, Lj1/f;->c:Lj1/f$d;

    .line 2
    .line 3
    return-object v0
.end method

.method public bridge synthetic findViewById(I)Landroid/view/View;
    .locals 0

    .line 1
    invoke-super {p0, p1}, Lj1/c;->findViewById(I)Landroid/view/View;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method g(Lj1/b;Z)Landroid/graphics/drawable/Drawable;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    if-eqz p2, :cond_2

    .line 3
    .line 4
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 5
    .line 6
    iget p2, p1, Lj1/f$d;->F0:I

    .line 7
    .line 8
    if-eqz p2, :cond_0

    .line 9
    .line 10
    iget-object p1, p1, Lj1/f$d;->a:Landroid/content/Context;

    .line 11
    .line 12
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    iget-object p2, p0, Lj1/f;->c:Lj1/f$d;

    .line 17
    .line 18
    iget p2, p2, Lj1/f$d;->F0:I

    .line 19
    .line 20
    invoke-static {p1, p2, v0}, Landroidx/core/content/res/h;->f(Landroid/content/res/Resources;ILandroid/content/res/Resources$Theme;)Landroid/graphics/drawable/Drawable;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    return-object p1

    .line 25
    :cond_0
    iget-object p1, p1, Lj1/f$d;->a:Landroid/content/Context;

    .line 26
    .line 27
    sget p2, Lj1/g;->j:I

    .line 28
    .line 29
    invoke-static {p1, p2}, Ll1/a;->p(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    if-eqz p1, :cond_1

    .line 34
    .line 35
    return-object p1

    .line 36
    :cond_1
    invoke-virtual {p0}, Landroid/app/Dialog;->getContext()Landroid/content/Context;

    .line 37
    .line 38
    .line 39
    move-result-object p1

    .line 40
    invoke-static {p1, p2}, Ll1/a;->p(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    return-object p1

    .line 45
    :cond_2
    sget-object p2, Lj1/f$c;->a:[I

    .line 46
    .line 47
    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    .line 48
    .line 49
    .line 50
    move-result p1

    .line 51
    aget p1, p2, p1

    .line 52
    .line 53
    const/4 p2, 0x1

    .line 54
    if-eq p1, p2, :cond_8

    .line 55
    .line 56
    const/4 p2, 0x2

    .line 57
    if-eq p1, p2, :cond_5

    .line 58
    .line 59
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 60
    .line 61
    iget p2, p1, Lj1/f$d;->G0:I

    .line 62
    .line 63
    if-eqz p2, :cond_3

    .line 64
    .line 65
    iget-object p1, p1, Lj1/f$d;->a:Landroid/content/Context;

    .line 66
    .line 67
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 68
    .line 69
    .line 70
    move-result-object p1

    .line 71
    iget-object p2, p0, Lj1/f;->c:Lj1/f$d;

    .line 72
    .line 73
    iget p2, p2, Lj1/f$d;->G0:I

    .line 74
    .line 75
    invoke-static {p1, p2, v0}, Landroidx/core/content/res/h;->f(Landroid/content/res/Resources;ILandroid/content/res/Resources$Theme;)Landroid/graphics/drawable/Drawable;

    .line 76
    .line 77
    .line 78
    move-result-object p1

    .line 79
    return-object p1

    .line 80
    :cond_3
    iget-object p1, p1, Lj1/f$d;->a:Landroid/content/Context;

    .line 81
    .line 82
    sget p2, Lj1/g;->h:I

    .line 83
    .line 84
    invoke-static {p1, p2}, Ll1/a;->p(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 85
    .line 86
    .line 87
    move-result-object p1

    .line 88
    if-eqz p1, :cond_4

    .line 89
    .line 90
    return-object p1

    .line 91
    :cond_4
    invoke-virtual {p0}, Landroid/app/Dialog;->getContext()Landroid/content/Context;

    .line 92
    .line 93
    .line 94
    move-result-object p1

    .line 95
    invoke-static {p1, p2}, Ll1/a;->p(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 96
    .line 97
    .line 98
    move-result-object p1

    .line 99
    iget-object p2, p0, Lj1/f;->c:Lj1/f$d;

    .line 100
    .line 101
    iget p2, p2, Lj1/f$d;->h:I

    .line 102
    .line 103
    invoke-static {p1, p2}, Ll1/b;->a(Landroid/graphics/drawable/Drawable;I)V

    .line 104
    .line 105
    .line 106
    return-object p1

    .line 107
    :cond_5
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 108
    .line 109
    iget p2, p1, Lj1/f$d;->I0:I

    .line 110
    .line 111
    if-eqz p2, :cond_6

    .line 112
    .line 113
    iget-object p1, p1, Lj1/f$d;->a:Landroid/content/Context;

    .line 114
    .line 115
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 116
    .line 117
    .line 118
    move-result-object p1

    .line 119
    iget-object p2, p0, Lj1/f;->c:Lj1/f$d;

    .line 120
    .line 121
    iget p2, p2, Lj1/f$d;->I0:I

    .line 122
    .line 123
    invoke-static {p1, p2, v0}, Landroidx/core/content/res/h;->f(Landroid/content/res/Resources;ILandroid/content/res/Resources$Theme;)Landroid/graphics/drawable/Drawable;

    .line 124
    .line 125
    .line 126
    move-result-object p1

    .line 127
    return-object p1

    .line 128
    :cond_6
    iget-object p1, p1, Lj1/f$d;->a:Landroid/content/Context;

    .line 129
    .line 130
    sget p2, Lj1/g;->f:I

    .line 131
    .line 132
    invoke-static {p1, p2}, Ll1/a;->p(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 133
    .line 134
    .line 135
    move-result-object p1

    .line 136
    if-eqz p1, :cond_7

    .line 137
    .line 138
    return-object p1

    .line 139
    :cond_7
    invoke-virtual {p0}, Landroid/app/Dialog;->getContext()Landroid/content/Context;

    .line 140
    .line 141
    .line 142
    move-result-object p1

    .line 143
    invoke-static {p1, p2}, Ll1/a;->p(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 144
    .line 145
    .line 146
    move-result-object p1

    .line 147
    iget-object p2, p0, Lj1/f;->c:Lj1/f$d;

    .line 148
    .line 149
    iget p2, p2, Lj1/f$d;->h:I

    .line 150
    .line 151
    invoke-static {p1, p2}, Ll1/b;->a(Landroid/graphics/drawable/Drawable;I)V

    .line 152
    .line 153
    .line 154
    return-object p1

    .line 155
    :cond_8
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 156
    .line 157
    iget p2, p1, Lj1/f$d;->H0:I

    .line 158
    .line 159
    if-eqz p2, :cond_9

    .line 160
    .line 161
    iget-object p1, p1, Lj1/f$d;->a:Landroid/content/Context;

    .line 162
    .line 163
    invoke-virtual {p1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 164
    .line 165
    .line 166
    move-result-object p1

    .line 167
    iget-object p2, p0, Lj1/f;->c:Lj1/f$d;

    .line 168
    .line 169
    iget p2, p2, Lj1/f$d;->H0:I

    .line 170
    .line 171
    invoke-static {p1, p2, v0}, Landroidx/core/content/res/h;->f(Landroid/content/res/Resources;ILandroid/content/res/Resources$Theme;)Landroid/graphics/drawable/Drawable;

    .line 172
    .line 173
    .line 174
    move-result-object p1

    .line 175
    return-object p1

    .line 176
    :cond_9
    iget-object p1, p1, Lj1/f$d;->a:Landroid/content/Context;

    .line 177
    .line 178
    sget p2, Lj1/g;->g:I

    .line 179
    .line 180
    invoke-static {p1, p2}, Ll1/a;->p(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 181
    .line 182
    .line 183
    move-result-object p1

    .line 184
    if-eqz p1, :cond_a

    .line 185
    .line 186
    return-object p1

    .line 187
    :cond_a
    invoke-virtual {p0}, Landroid/app/Dialog;->getContext()Landroid/content/Context;

    .line 188
    .line 189
    .line 190
    move-result-object p1

    .line 191
    invoke-static {p1, p2}, Ll1/a;->p(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 192
    .line 193
    .line 194
    move-result-object p1

    .line 195
    iget-object p2, p0, Lj1/f;->c:Lj1/f$d;

    .line 196
    .line 197
    iget p2, p2, Lj1/f$d;->h:I

    .line 198
    .line 199
    invoke-static {p1, p2}, Ll1/b;->a(Landroid/graphics/drawable/Drawable;I)V

    .line 200
    .line 201
    .line 202
    return-object p1
.end method

.method public final h()Landroid/widget/EditText;
    .locals 1

    .line 1
    iget-object v0, p0, Lj1/f;->h:Landroid/widget/EditText;

    .line 2
    .line 3
    return-object v0
.end method

.method final i()Landroid/graphics/drawable/Drawable;
    .locals 3

    .line 1
    iget-object v0, p0, Lj1/f;->c:Lj1/f$d;

    .line 2
    .line 3
    iget v1, v0, Lj1/f$d;->E0:I

    .line 4
    .line 5
    if-eqz v1, :cond_0

    .line 6
    .line 7
    iget-object v0, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 8
    .line 9
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    iget-object v1, p0, Lj1/f;->c:Lj1/f$d;

    .line 14
    .line 15
    iget v1, v1, Lj1/f$d;->E0:I

    .line 16
    .line 17
    const/4 v2, 0x0

    .line 18
    invoke-static {v0, v1, v2}, Landroidx/core/content/res/h;->f(Landroid/content/res/Resources;ILandroid/content/res/Resources$Theme;)Landroid/graphics/drawable/Drawable;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    return-object v0

    .line 23
    :cond_0
    iget-object v0, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 24
    .line 25
    sget v1, Lj1/g;->x:I

    .line 26
    .line 27
    invoke-static {v0, v1}, Ll1/a;->p(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    if-eqz v0, :cond_1

    .line 32
    .line 33
    return-object v0

    .line 34
    :cond_1
    invoke-virtual {p0}, Landroid/app/Dialog;->getContext()Landroid/content/Context;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    invoke-static {v0, v1}, Ll1/a;->p(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    return-object v0
.end method

.method public final j()Landroid/view/View;
    .locals 1

    .line 1
    iget-object v0, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 2
    .line 3
    return-object v0
.end method

.method k(IZ)V
    .locals 6

    .line 1
    iget-object v0, p0, Lj1/f;->t:Landroid/widget/TextView;

    .line 2
    .line 3
    if-eqz v0, :cond_8

    .line 4
    .line 5
    iget-object v1, p0, Lj1/f;->c:Lj1/f$d;

    .line 6
    .line 7
    iget v1, v1, Lj1/f$d;->n0:I

    .line 8
    .line 9
    const/4 v2, 0x1

    .line 10
    const/4 v3, 0x0

    .line 11
    if-lez v1, :cond_0

    .line 12
    .line 13
    invoke-static {}, Ljava/util/Locale;->getDefault()Ljava/util/Locale;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    const/4 v4, 0x2

    .line 18
    new-array v4, v4, [Ljava/lang/Object;

    .line 19
    .line 20
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 21
    .line 22
    .line 23
    move-result-object v5

    .line 24
    aput-object v5, v4, v3

    .line 25
    .line 26
    iget-object v5, p0, Lj1/f;->c:Lj1/f$d;

    .line 27
    .line 28
    iget v5, v5, Lj1/f$d;->n0:I

    .line 29
    .line 30
    invoke-static {v5}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 31
    .line 32
    .line 33
    move-result-object v5

    .line 34
    aput-object v5, v4, v2

    .line 35
    .line 36
    const-string v5, "%d/%d"

    .line 37
    .line 38
    invoke-static {v1, v5, v4}, Ljava/lang/String;->format(Ljava/util/Locale;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 39
    .line 40
    .line 41
    move-result-object v1

    .line 42
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 43
    .line 44
    .line 45
    iget-object v0, p0, Lj1/f;->t:Landroid/widget/TextView;

    .line 46
    .line 47
    invoke-virtual {v0, v3}, Landroid/view/View;->setVisibility(I)V

    .line 48
    .line 49
    .line 50
    goto :goto_0

    .line 51
    :cond_0
    const/16 v1, 0x8

    .line 52
    .line 53
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 54
    .line 55
    .line 56
    :goto_0
    if-eqz p2, :cond_1

    .line 57
    .line 58
    if-eqz p1, :cond_3

    .line 59
    .line 60
    :cond_1
    iget-object p2, p0, Lj1/f;->c:Lj1/f$d;

    .line 61
    .line 62
    iget v0, p2, Lj1/f$d;->n0:I

    .line 63
    .line 64
    if-lez v0, :cond_2

    .line 65
    .line 66
    if-gt p1, v0, :cond_3

    .line 67
    .line 68
    :cond_2
    iget p2, p2, Lj1/f$d;->m0:I

    .line 69
    .line 70
    if-ge p1, p2, :cond_4

    .line 71
    .line 72
    :cond_3
    const/4 v3, 0x1

    .line 73
    :cond_4
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 74
    .line 75
    if-eqz v3, :cond_5

    .line 76
    .line 77
    iget p1, p1, Lj1/f$d;->o0:I

    .line 78
    .line 79
    goto :goto_1

    .line 80
    :cond_5
    iget p1, p1, Lj1/f$d;->j:I

    .line 81
    .line 82
    :goto_1
    iget-object p2, p0, Lj1/f;->c:Lj1/f$d;

    .line 83
    .line 84
    if-eqz v3, :cond_6

    .line 85
    .line 86
    iget p2, p2, Lj1/f$d;->o0:I

    .line 87
    .line 88
    goto :goto_2

    .line 89
    :cond_6
    iget p2, p2, Lj1/f$d;->t:I

    .line 90
    .line 91
    :goto_2
    iget-object v0, p0, Lj1/f;->c:Lj1/f$d;

    .line 92
    .line 93
    iget v0, v0, Lj1/f$d;->n0:I

    .line 94
    .line 95
    if-lez v0, :cond_7

    .line 96
    .line 97
    iget-object v0, p0, Lj1/f;->t:Landroid/widget/TextView;

    .line 98
    .line 99
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setTextColor(I)V

    .line 100
    .line 101
    .line 102
    :cond_7
    iget-object p1, p0, Lj1/f;->h:Landroid/widget/EditText;

    .line 103
    .line 104
    invoke-static {p1, p2}, Lk1/b;->e(Landroid/widget/EditText;I)V

    .line 105
    .line 106
    .line 107
    sget-object p1, Lj1/b;->a:Lj1/b;

    .line 108
    .line 109
    invoke-virtual {p0, p1}, Lj1/f;->e(Lj1/b;)Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 110
    .line 111
    .line 112
    move-result-object p1

    .line 113
    xor-int/lit8 p2, v3, 0x1

    .line 114
    .line 115
    invoke-virtual {p1, p2}, Landroid/view/View;->setEnabled(Z)V

    .line 116
    .line 117
    .line 118
    :cond_8
    return-void
.end method

.method final l()V
    .locals 3

    .line 1
    iget-object v0, p0, Lj1/f;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    return-void

    .line 6
    :cond_0
    iget-object v0, p0, Lj1/f;->c:Lj1/f$d;

    .line 7
    .line 8
    iget-object v0, v0, Lj1/f$d;->l:Ljava/util/ArrayList;

    .line 9
    .line 10
    if-eqz v0, :cond_1

    .line 11
    .line 12
    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-nez v0, :cond_2

    .line 17
    .line 18
    :cond_1
    iget-object v0, p0, Lj1/f;->c:Lj1/f$d;

    .line 19
    .line 20
    iget-object v0, v0, Lj1/f$d;->S:Landroidx/recyclerview/widget/RecyclerView$h;

    .line 21
    .line 22
    if-nez v0, :cond_2

    .line 23
    .line 24
    return-void

    .line 25
    :cond_2
    iget-object v0, p0, Lj1/f;->c:Lj1/f$d;

    .line 26
    .line 27
    iget-object v1, v0, Lj1/f$d;->T:Landroidx/recyclerview/widget/RecyclerView$p;

    .line 28
    .line 29
    if-nez v1, :cond_3

    .line 30
    .line 31
    new-instance v1, Landroidx/recyclerview/widget/LinearLayoutManager;

    .line 32
    .line 33
    invoke-virtual {p0}, Landroid/app/Dialog;->getContext()Landroid/content/Context;

    .line 34
    .line 35
    .line 36
    move-result-object v2

    .line 37
    invoke-direct {v1, v2}, Landroidx/recyclerview/widget/LinearLayoutManager;-><init>(Landroid/content/Context;)V

    .line 38
    .line 39
    .line 40
    iput-object v1, v0, Lj1/f$d;->T:Landroidx/recyclerview/widget/RecyclerView$p;

    .line 41
    .line 42
    :cond_3
    iget-object v0, p0, Lj1/f;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 43
    .line 44
    invoke-virtual {v0}, Landroidx/recyclerview/widget/RecyclerView;->getLayoutManager()Landroidx/recyclerview/widget/RecyclerView$p;

    .line 45
    .line 46
    .line 47
    move-result-object v0

    .line 48
    if-nez v0, :cond_4

    .line 49
    .line 50
    iget-object v0, p0, Lj1/f;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 51
    .line 52
    iget-object v1, p0, Lj1/f;->c:Lj1/f$d;

    .line 53
    .line 54
    iget-object v1, v1, Lj1/f$d;->T:Landroidx/recyclerview/widget/RecyclerView$p;

    .line 55
    .line 56
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setLayoutManager(Landroidx/recyclerview/widget/RecyclerView$p;)V

    .line 57
    .line 58
    .line 59
    :cond_4
    iget-object v0, p0, Lj1/f;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 60
    .line 61
    iget-object v1, p0, Lj1/f;->c:Lj1/f$d;

    .line 62
    .line 63
    iget-object v1, v1, Lj1/f$d;->S:Landroidx/recyclerview/widget/RecyclerView$h;

    .line 64
    .line 65
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView;->setAdapter(Landroidx/recyclerview/widget/RecyclerView$h;)V

    .line 66
    .line 67
    .line 68
    iget-object v0, p0, Lj1/f;->y:Lj1/f$f;

    .line 69
    .line 70
    if-eqz v0, :cond_5

    .line 71
    .line 72
    iget-object v0, p0, Lj1/f;->c:Lj1/f$d;

    .line 73
    .line 74
    iget-object v0, v0, Lj1/f$d;->S:Landroidx/recyclerview/widget/RecyclerView$h;

    .line 75
    .line 76
    check-cast v0, Lj1/a;

    .line 77
    .line 78
    invoke-virtual {v0, p0}, Lj1/a;->Z(Lj1/a$c;)V

    .line 79
    .line 80
    .line 81
    :cond_5
    return-void
.end method

.method o()V
    .locals 2

    .line 1
    iget-object v0, p0, Lj1/f;->h:Landroid/widget/EditText;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    return-void

    .line 6
    :cond_0
    new-instance v1, Lj1/f$b;

    .line 7
    .line 8
    invoke-direct {v1, p0}, Lj1/f$b;-><init>(Lj1/f;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {v0, v1}, Landroid/widget/TextView;->addTextChangedListener(Landroid/text/TextWatcher;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public final onClick(Landroid/view/View;)V
    .locals 3

    .line 1
    invoke-virtual {p1}, Landroid/view/View;->getTag()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    check-cast v0, Lj1/b;

    .line 6
    .line 7
    sget-object v1, Lj1/f$c;->a:[I

    .line 8
    .line 9
    invoke-virtual {v0}, Ljava/lang/Enum;->ordinal()I

    .line 10
    .line 11
    .line 12
    move-result v2

    .line 13
    aget v1, v1, v2

    .line 14
    .line 15
    const/4 v2, 0x1

    .line 16
    if-eq v1, v2, :cond_6

    .line 17
    .line 18
    const/4 v2, 0x2

    .line 19
    if-eq v1, v2, :cond_4

    .line 20
    .line 21
    const/4 v2, 0x3

    .line 22
    if-eq v1, v2, :cond_0

    .line 23
    .line 24
    goto :goto_0

    .line 25
    :cond_0
    iget-object v1, p0, Lj1/f;->c:Lj1/f$d;

    .line 26
    .line 27
    invoke-virtual {v1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 28
    .line 29
    .line 30
    iget-object v1, p0, Lj1/f;->c:Lj1/f$d;

    .line 31
    .line 32
    iget-object v1, v1, Lj1/f$d;->z:Lj1/f$g;

    .line 33
    .line 34
    if-eqz v1, :cond_1

    .line 35
    .line 36
    invoke-interface {v1, p0, v0}, Lj1/f$g;->a(Lj1/f;Lj1/b;)V

    .line 37
    .line 38
    .line 39
    :cond_1
    iget-object v1, p0, Lj1/f;->c:Lj1/f$d;

    .line 40
    .line 41
    iget-boolean v1, v1, Lj1/f$d;->E:Z

    .line 42
    .line 43
    if-nez v1, :cond_2

    .line 44
    .line 45
    invoke-direct {p0, p1}, Lj1/f;->n(Landroid/view/View;)Z

    .line 46
    .line 47
    .line 48
    :cond_2
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 49
    .line 50
    iget-boolean p1, p1, Lj1/f$d;->D:Z

    .line 51
    .line 52
    if-nez p1, :cond_3

    .line 53
    .line 54
    invoke-direct {p0}, Lj1/f;->m()Z

    .line 55
    .line 56
    .line 57
    :cond_3
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 58
    .line 59
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 60
    .line 61
    .line 62
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 63
    .line 64
    iget-boolean p1, p1, Lj1/f$d;->M:Z

    .line 65
    .line 66
    if-eqz p1, :cond_8

    .line 67
    .line 68
    invoke-virtual {p0}, Lj1/f;->dismiss()V

    .line 69
    .line 70
    .line 71
    goto :goto_0

    .line 72
    :cond_4
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 73
    .line 74
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 75
    .line 76
    .line 77
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 78
    .line 79
    iget-object p1, p1, Lj1/f$d;->A:Lj1/f$g;

    .line 80
    .line 81
    if-eqz p1, :cond_5

    .line 82
    .line 83
    invoke-interface {p1, p0, v0}, Lj1/f$g;->a(Lj1/f;Lj1/b;)V

    .line 84
    .line 85
    .line 86
    :cond_5
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 87
    .line 88
    iget-boolean p1, p1, Lj1/f$d;->M:Z

    .line 89
    .line 90
    if-eqz p1, :cond_8

    .line 91
    .line 92
    invoke-virtual {p0}, Landroid/app/Dialog;->cancel()V

    .line 93
    .line 94
    .line 95
    goto :goto_0

    .line 96
    :cond_6
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 97
    .line 98
    invoke-virtual {p1}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 99
    .line 100
    .line 101
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 102
    .line 103
    iget-object p1, p1, Lj1/f$d;->B:Lj1/f$g;

    .line 104
    .line 105
    if-eqz p1, :cond_7

    .line 106
    .line 107
    invoke-interface {p1, p0, v0}, Lj1/f$g;->a(Lj1/f;Lj1/b;)V

    .line 108
    .line 109
    .line 110
    :cond_7
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 111
    .line 112
    iget-boolean p1, p1, Lj1/f$d;->M:Z

    .line 113
    .line 114
    if-eqz p1, :cond_8

    .line 115
    .line 116
    invoke-virtual {p0}, Lj1/f;->dismiss()V

    .line 117
    .line 118
    .line 119
    :cond_8
    :goto_0
    iget-object p1, p0, Lj1/f;->c:Lj1/f$d;

    .line 120
    .line 121
    iget-object p1, p1, Lj1/f$d;->C:Lj1/f$g;

    .line 122
    .line 123
    if-eqz p1, :cond_9

    .line 124
    .line 125
    invoke-interface {p1, p0, v0}, Lj1/f$g;->a(Lj1/f;Lj1/b;)V

    .line 126
    .line 127
    .line 128
    :cond_9
    return-void
.end method

.method public final onShow(Landroid/content/DialogInterface;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lj1/f;->h:Landroid/widget/EditText;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Lj1/f;->c:Lj1/f$d;

    .line 6
    .line 7
    invoke-static {p0, v0}, Ll1/a;->u(Landroid/content/DialogInterface;Lj1/f$d;)V

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, Lj1/f;->h:Landroid/widget/EditText;

    .line 11
    .line 12
    invoke-virtual {v0}, Landroid/widget/EditText;->getText()Landroid/text/Editable;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-interface {v0}, Ljava/lang/CharSequence;->length()I

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    if-lez v0, :cond_0

    .line 21
    .line 22
    iget-object v0, p0, Lj1/f;->h:Landroid/widget/EditText;

    .line 23
    .line 24
    invoke-virtual {v0}, Landroid/widget/EditText;->getText()Landroid/text/Editable;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    .line 29
    .line 30
    .line 31
    move-result v1

    .line 32
    invoke-virtual {v0, v1}, Landroid/widget/EditText;->setSelection(I)V

    .line 33
    .line 34
    .line 35
    :cond_0
    invoke-super {p0, p1}, Lj1/c;->onShow(Landroid/content/DialogInterface;)V

    .line 36
    .line 37
    .line 38
    return-void
.end method

.method public final p(Landroid/widget/TextView;Landroid/graphics/Typeface;)V
    .locals 1

    .line 1
    if-nez p2, :cond_0

    .line 2
    .line 3
    return-void

    .line 4
    :cond_0
    invoke-virtual {p1}, Landroid/widget/TextView;->getPaintFlags()I

    .line 5
    .line 6
    .line 7
    move-result v0

    .line 8
    or-int/lit16 v0, v0, 0x80

    .line 9
    .line 10
    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setPaintFlags(I)V

    .line 11
    .line 12
    .line 13
    invoke-virtual {p1, p2}, Landroid/widget/TextView;->setTypeface(Landroid/graphics/Typeface;)V

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public bridge synthetic setContentView(I)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/IllegalAccessError;
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 1
    invoke-super {p0, p1}, Lj1/c;->setContentView(I)V

    return-void
.end method

.method public bridge synthetic setContentView(Landroid/view/View;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/IllegalAccessError;
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 2
    invoke-super {p0, p1}, Lj1/c;->setContentView(Landroid/view/View;)V

    return-void
.end method

.method public bridge synthetic setContentView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/lang/IllegalAccessError;
        }
    .end annotation

    .annotation runtime Ljava/lang/Deprecated;
    .end annotation

    .line 3
    invoke-super {p0, p1, p2}, Lj1/c;->setContentView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    return-void
.end method

.method public final setTitle(I)V
    .locals 1

    .line 2
    iget-object v0, p0, Lj1/f;->c:Lj1/f$d;

    iget-object v0, v0, Lj1/f$d;->a:Landroid/content/Context;

    invoke-virtual {v0, p1}, Landroid/content/Context;->getString(I)Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p0, p1}, Lj1/f;->setTitle(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public final setTitle(Ljava/lang/CharSequence;)V
    .locals 1

    .line 1
    iget-object v0, p0, Lj1/f;->f:Landroid/widget/TextView;

    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    return-void
.end method

.method public show()V
    .locals 2

    .line 1
    :try_start_0
    invoke-super {p0}, Landroid/app/Dialog;->show()V
    :try_end_0
    .catch Landroid/view/WindowManager$BadTokenException; {:try_start_0 .. :try_end_0} :catch_0

    .line 2
    .line 3
    .line 4
    return-void

    .line 5
    :catch_0
    new-instance v0, Lj1/f$e;

    .line 6
    .line 7
    const-string v1, "Bad window token, you cannot show a dialog before an Activity is created or after it\'s hidden."

    .line 8
    .line 9
    invoke-direct {v0, v1}, Lj1/f$e;-><init>(Ljava/lang/String;)V

    .line 10
    .line 11
    .line 12
    throw v0
.end method
