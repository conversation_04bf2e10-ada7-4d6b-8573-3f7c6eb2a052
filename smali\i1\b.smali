.class public final Li1/b;
.super Ljava/lang/Object;
.source "MaterialCab.kt"

# interfaces
.implements Landroidx/appcompat/widget/Toolbar$f;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Li1/b$a;
    }
.end annotation


# static fields
.field private static s:Li1/b;
    .annotation build Landroid/annotation/SuppressLint;
        value = {
            "StaticFieldLeak"
        }
    .end annotation
.end field

.field public static final t:Li1/b$a;


# instance fields
.field private a:Ljb/p;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljb/p<",
            "-",
            "Li1/b;",
            "-",
            "Landroid/view/Menu;",
            "Lwa/q;",
            ">;"
        }
    .end annotation
.end field

.field private b:Ljb/p;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljb/p<",
            "-",
            "Li1/b;",
            "-",
            "Landroid/view/Menu;",
            "Lwa/q;",
            ">;"
        }
    .end annotation
.end field

.field private c:Ljb/l;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljb/l<",
            "-",
            "Landroid/view/MenuItem;",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field private d:Ljb/l;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljb/l<",
            "-",
            "Li1/b;",
            "Ljava/lang/Boolean;",
            ">;"
        }
    .end annotation
.end field

.field private e:Landroidx/appcompat/widget/Toolbar;

.field private f:Ljb/p;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljb/p<",
            "-",
            "Landroid/view/View;",
            "-",
            "Landroid/view/ViewPropertyAnimator;",
            "Lwa/q;",
            ">;"
        }
    .end annotation
.end field

.field private g:Ljb/p;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljb/p<",
            "-",
            "Landroid/view/View;",
            "-",
            "Landroid/view/ViewPropertyAnimator;",
            "Lwa/q;",
            ">;"
        }
    .end annotation
.end field

.field private h:Ljava/lang/String;

.field private i:Ljava/lang/String;

.field private j:I

.field private k:I

.field private l:I

.field private m:I

.field private n:I

.field private o:I

.field private p:I

.field private q:Ld/c;

.field private r:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Li1/b$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Li1/b$a;-><init>(Lkb/g;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, Li1/b;->t:Li1/b$a;

    .line 8
    .line 9
    return-void
.end method

.method public constructor <init>(Ld/c;I)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Li1/b;->q:Ld/c;

    .line 5
    .line 6
    iput p2, p0, Li1/b;->r:I

    .line 7
    .line 8
    const/4 p1, -0x1

    .line 9
    iput p1, p0, Li1/b;->j:I

    .line 10
    .line 11
    iput p1, p0, Li1/b;->k:I

    .line 12
    .line 13
    sget p1, Li1/h;->a:I

    .line 14
    .line 15
    iput p1, p0, Li1/b;->l:I

    .line 16
    .line 17
    invoke-direct {p0}, Li1/b;->e()Ld/c;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    sget p2, Li1/d;->a:I

    .line 22
    .line 23
    invoke-static {p1, p2}, Li1/a;->b(Landroid/content/Context;I)I

    .line 24
    .line 25
    .line 26
    move-result p1

    .line 27
    iput p1, p0, Li1/b;->m:I

    .line 28
    .line 29
    invoke-direct {p0}, Li1/b;->e()Ld/c;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    sget p2, Li1/c;->a:I

    .line 34
    .line 35
    const v0, -0x777778

    .line 36
    .line 37
    .line 38
    invoke-static {p1, p2, v0}, Li1/a;->a(Landroid/content/Context;II)I

    .line 39
    .line 40
    .line 41
    move-result p1

    .line 42
    iput p1, p0, Li1/b;->o:I

    .line 43
    .line 44
    sget p1, Li1/e;->a:I

    .line 45
    .line 46
    iput p1, p0, Li1/b;->p:I

    .line 47
    .line 48
    return-void
.end method

.method public static final synthetic a(Li1/b;)Ljb/l;
    .locals 0

    .line 1
    iget-object p0, p0, Li1/b;->d:Ljb/l;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic b()Li1/b;
    .locals 1

    .line 1
    sget-object v0, Li1/b;->s:Li1/b;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final synthetic c(Li1/b;Ld/c;)V
    .locals 0

    .line 1
    iput-object p1, p0, Li1/b;->q:Ld/c;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic d(Li1/b;)V
    .locals 0

    .line 1
    sput-object p0, Li1/b;->s:Li1/b;

    .line 2
    .line 3
    return-void
.end method

.method private final e()Ld/c;
    .locals 1

    .line 1
    iget-object v0, p0, Li1/b;->q:Ld/c;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    invoke-static {}, Lkb/l;->q()V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-object v0
.end method


# virtual methods
.method public final f()Ljb/p;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljb/p<",
            "Landroid/view/View;",
            "Landroid/view/ViewPropertyAnimator;",
            "Lwa/q;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Li1/b;->f:Ljb/p;

    .line 2
    .line 3
    return-object v0
.end method

.method public final g()Ljb/p;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Ljb/p<",
            "Landroid/view/View;",
            "Landroid/view/ViewPropertyAnimator;",
            "Lwa/q;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Li1/b;->g:Ljb/p;

    .line 2
    .line 3
    return-object v0
.end method

.method public final h()Landroid/view/Menu;
    .locals 1

    .line 1
    iget-object v0, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-virtual {v0}, Landroidx/appcompat/widget/Toolbar;->getMenu()Landroid/view/Menu;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    goto :goto_0

    .line 10
    :cond_0
    const/4 v0, 0x0

    .line 11
    :goto_0
    return-object v0
.end method

.method public final i()Landroidx/appcompat/widget/Toolbar;
    .locals 1

    .line 1
    iget-object v0, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 2
    .line 3
    return-object v0
.end method

.method public final j(Z)V
    .locals 6

    .line 1
    invoke-direct {p0}, Li1/b;->e()Ld/c;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    iget v1, p0, Li1/b;->r:I

    .line 6
    .line 7
    invoke-virtual {v0, v1}, Ld/c;->findViewById(I)Landroid/view/View;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    sget v2, Li1/f;->a:I

    .line 12
    .line 13
    invoke-virtual {v0, v2}, Ld/c;->findViewById(I)Landroid/view/View;

    .line 14
    .line 15
    .line 16
    move-result-object v3

    .line 17
    const/4 v4, 0x0

    .line 18
    const-string v5, "null cannot be cast to non-null type androidx.appcompat.widget.Toolbar"

    .line 19
    .line 20
    if-eqz v3, :cond_1

    .line 21
    .line 22
    invoke-virtual {v0, v2}, Ld/c;->findViewById(I)Landroid/view/View;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    if-eqz v0, :cond_0

    .line 27
    .line 28
    check-cast v0, Landroidx/appcompat/widget/Toolbar;

    .line 29
    .line 30
    iput-object v0, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 31
    .line 32
    goto :goto_0

    .line 33
    :cond_0
    new-instance p1, Lkotlin/TypeCastException;

    .line 34
    .line 35
    invoke-direct {p1, v5}, Lkotlin/TypeCastException;-><init>(Ljava/lang/String;)V

    .line 36
    .line 37
    .line 38
    throw p1

    .line 39
    :cond_1
    instance-of v3, v1, Landroid/view/ViewStub;

    .line 40
    .line 41
    if-eqz v3, :cond_3

    .line 42
    .line 43
    check-cast v1, Landroid/view/ViewStub;

    .line 44
    .line 45
    sget v0, Li1/g;->a:I

    .line 46
    .line 47
    invoke-virtual {v1, v0}, Landroid/view/ViewStub;->setLayoutResource(I)V

    .line 48
    .line 49
    .line 50
    invoke-virtual {v1, v2}, Landroid/view/ViewStub;->setInflatedId(I)V

    .line 51
    .line 52
    .line 53
    invoke-virtual {v1}, Landroid/view/ViewStub;->inflate()Landroid/view/View;

    .line 54
    .line 55
    .line 56
    move-result-object v0

    .line 57
    if-eqz v0, :cond_2

    .line 58
    .line 59
    check-cast v0, Landroidx/appcompat/widget/Toolbar;

    .line 60
    .line 61
    iput-object v0, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 62
    .line 63
    goto :goto_0

    .line 64
    :cond_2
    new-instance p1, Lkotlin/TypeCastException;

    .line 65
    .line 66
    invoke-direct {p1, v5}, Lkotlin/TypeCastException;-><init>(Ljava/lang/String;)V

    .line 67
    .line 68
    .line 69
    throw p1

    .line 70
    :cond_3
    instance-of v3, v1, Landroid/view/ViewGroup;

    .line 71
    .line 72
    if-eqz v3, :cond_8

    .line 73
    .line 74
    invoke-static {v0}, Landroid/view/LayoutInflater;->from(Landroid/content/Context;)Landroid/view/LayoutInflater;

    .line 75
    .line 76
    .line 77
    move-result-object v0

    .line 78
    sget v3, Li1/g;->a:I

    .line 79
    .line 80
    check-cast v1, Landroid/view/ViewGroup;

    .line 81
    .line 82
    invoke-virtual {v0, v3, v1, v4}, Landroid/view/LayoutInflater;->inflate(ILandroid/view/ViewGroup;Z)Landroid/view/View;

    .line 83
    .line 84
    .line 85
    move-result-object v0

    .line 86
    if-eqz v0, :cond_7

    .line 87
    .line 88
    check-cast v0, Landroidx/appcompat/widget/Toolbar;

    .line 89
    .line 90
    iput-object v0, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 91
    .line 92
    invoke-virtual {v1, v0}, Landroid/view/ViewGroup;->addView(Landroid/view/View;)V

    .line 93
    .line 94
    .line 95
    :goto_0
    iget-object v0, p0, Li1/b;->h:Ljava/lang/String;

    .line 96
    .line 97
    invoke-virtual {p0, v0}, Li1/b;->t(Ljava/lang/String;)V

    .line 98
    .line 99
    .line 100
    iget v0, p0, Li1/b;->j:I

    .line 101
    .line 102
    invoke-virtual {p0, v0}, Li1/b;->u(I)V

    .line 103
    .line 104
    .line 105
    iget-object v0, p0, Li1/b;->i:Ljava/lang/String;

    .line 106
    .line 107
    invoke-virtual {p0, v0}, Li1/b;->r(Ljava/lang/String;)V

    .line 108
    .line 109
    .line 110
    iget v0, p0, Li1/b;->k:I

    .line 111
    .line 112
    invoke-virtual {p0, v0}, Li1/b;->s(I)V

    .line 113
    .line 114
    .line 115
    iget v0, p0, Li1/b;->l:I

    .line 116
    .line 117
    invoke-virtual {p0, v0}, Li1/b;->q(I)V

    .line 118
    .line 119
    .line 120
    iget v0, p0, Li1/b;->n:I

    .line 121
    .line 122
    invoke-virtual {p0, v0}, Li1/b;->p(I)V

    .line 123
    .line 124
    .line 125
    iget v0, p0, Li1/b;->p:I

    .line 126
    .line 127
    invoke-virtual {p0, v0}, Li1/b;->n(I)V

    .line 128
    .line 129
    .line 130
    iget v0, p0, Li1/b;->o:I

    .line 131
    .line 132
    invoke-virtual {p0, v0}, Li1/b;->m(I)V

    .line 133
    .line 134
    .line 135
    iget v0, p0, Li1/b;->m:I

    .line 136
    .line 137
    invoke-virtual {p0, v0}, Li1/b;->o(I)V

    .line 138
    .line 139
    .line 140
    iget-object v0, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 141
    .line 142
    if-eqz v0, :cond_6

    .line 143
    .line 144
    invoke-virtual {v0, v4}, Landroid/view/View;->setVisibility(I)V

    .line 145
    .line 146
    .line 147
    invoke-virtual {v0, v2}, Landroid/view/View;->setId(I)V

    .line 148
    .line 149
    .line 150
    sget-object v1, Li1/b$c;->a:Li1/b$c;

    .line 151
    .line 152
    invoke-virtual {v0, v1}, Landroidx/appcompat/widget/Toolbar;->setNavigationOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 153
    .line 154
    .line 155
    const-string v1, "menu"

    .line 156
    .line 157
    if-eqz p1, :cond_5

    .line 158
    .line 159
    iget-object v2, p0, Li1/b;->a:Ljb/p;

    .line 160
    .line 161
    if-eqz v2, :cond_4

    .line 162
    .line 163
    invoke-virtual {v0}, Landroidx/appcompat/widget/Toolbar;->getMenu()Landroid/view/Menu;

    .line 164
    .line 165
    .line 166
    move-result-object v3

    .line 167
    invoke-static {v3, v1}, Lkb/l;->d(Ljava/lang/Object;Ljava/lang/String;)V

    .line 168
    .line 169
    .line 170
    invoke-interface {v2, p0, v3}, Ljb/p;->p(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 171
    .line 172
    .line 173
    move-result-object v1

    .line 174
    check-cast v1, Lwa/q;

    .line 175
    .line 176
    :cond_4
    invoke-virtual {v0}, Landroid/view/View;->animate()Landroid/view/ViewPropertyAnimator;

    .line 177
    .line 178
    .line 179
    move-result-object v1

    .line 180
    const/4 v2, 0x0

    .line 181
    invoke-virtual {v1, v2}, Landroid/view/ViewPropertyAnimator;->setListener(Landroid/animation/Animator$AnimatorListener;)Landroid/view/ViewPropertyAnimator;

    .line 182
    .line 183
    .line 184
    move-result-object v1

    .line 185
    invoke-virtual {v1}, Landroid/view/ViewPropertyAnimator;->cancel()V

    .line 186
    .line 187
    .line 188
    invoke-virtual {v0}, Landroid/view/View;->getViewTreeObserver()Landroid/view/ViewTreeObserver;

    .line 189
    .line 190
    .line 191
    move-result-object v1

    .line 192
    new-instance v2, Li1/b$b;

    .line 193
    .line 194
    invoke-direct {v2, v0, v0, p0, p1}, Li1/b$b;-><init>(Landroid/view/View;Landroidx/appcompat/widget/Toolbar;Li1/b;Z)V

    .line 195
    .line 196
    .line 197
    invoke-virtual {v1, v2}, Landroid/view/ViewTreeObserver;->addOnGlobalLayoutListener(Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;)V

    .line 198
    .line 199
    .line 200
    goto :goto_1

    .line 201
    :cond_5
    iget-object p1, p0, Li1/b;->b:Ljb/p;

    .line 202
    .line 203
    if-eqz p1, :cond_6

    .line 204
    .line 205
    invoke-virtual {v0}, Landroidx/appcompat/widget/Toolbar;->getMenu()Landroid/view/Menu;

    .line 206
    .line 207
    .line 208
    move-result-object v0

    .line 209
    invoke-static {v0, v1}, Lkb/l;->d(Ljava/lang/Object;Ljava/lang/String;)V

    .line 210
    .line 211
    .line 212
    invoke-interface {p1, p0, v0}, Ljb/p;->p(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 213
    .line 214
    .line 215
    move-result-object p1

    .line 216
    check-cast p1, Lwa/q;

    .line 217
    .line 218
    :cond_6
    :goto_1
    return-void

    .line 219
    :cond_7
    new-instance p1, Lkotlin/TypeCastException;

    .line 220
    .line 221
    invoke-direct {p1, v5}, Lkotlin/TypeCastException;-><init>(Ljava/lang/String;)V

    .line 222
    .line 223
    .line 224
    throw p1

    .line 225
    :cond_8
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 226
    .line 227
    const-string v0, "MaterialCab was unable to attach to your Activity, attach to stub doesn\'t exist."

    .line 228
    .line 229
    invoke-direct {p1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 230
    .line 231
    .line 232
    throw p1
.end method

.method public final k(Ljb/l;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljb/l<",
            "-",
            "Li1/b;",
            "Ljava/lang/Boolean;",
            ">;)V"
        }
    .end annotation

    .line 1
    const-string v0, "callback"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkb/l;->i(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    iput-object p1, p0, Li1/b;->d:Ljb/l;

    .line 7
    .line 8
    return-void
.end method

.method public final l(Ljb/l;)V
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljb/l<",
            "-",
            "Landroid/view/MenuItem;",
            "Ljava/lang/Boolean;",
            ">;)V"
        }
    .end annotation

    .line 1
    const-string v0, "callback"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkb/l;->i(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    iput-object p1, p0, Li1/b;->c:Ljb/l;

    .line 7
    .line 8
    return-void
.end method

.method public final m(I)V
    .locals 1

    .line 1
    iput p1, p0, Li1/b;->o:I

    .line 2
    .line 3
    iget-object v0, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 4
    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Landroid/view/View;->setBackgroundColor(I)V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method

.method public final n(I)V
    .locals 2

    .line 1
    iput p1, p0, Li1/b;->p:I

    .line 2
    .line 3
    sget v0, Li1/e;->a:I

    .line 4
    .line 5
    if-ne p1, v0, :cond_0

    .line 6
    .line 7
    iget-object v0, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 8
    .line 9
    if-eqz v0, :cond_1

    .line 10
    .line 11
    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/Toolbar;->setNavigationIcon(I)V

    .line 12
    .line 13
    .line 14
    goto :goto_0

    .line 15
    :cond_0
    invoke-direct {p0}, Li1/b;->e()Ld/c;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-static {v0, p1}, Li1/a;->c(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    iget-object v0, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 24
    .line 25
    if-eqz v0, :cond_1

    .line 26
    .line 27
    iget v1, p0, Li1/b;->j:I

    .line 28
    .line 29
    invoke-static {p1, v1}, Li1/a;->d(Landroid/graphics/drawable/Drawable;I)Landroid/graphics/drawable/Drawable;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/Toolbar;->setNavigationIcon(Landroid/graphics/drawable/Drawable;)V

    .line 34
    .line 35
    .line 36
    :cond_1
    :goto_0
    return-void
.end method

.method public final o(I)V
    .locals 2

    .line 1
    iput p1, p0, Li1/b;->m:I

    .line 2
    .line 3
    iget-object v0, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 4
    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    const/4 v1, 0x0

    .line 8
    invoke-virtual {v0, p1, v1}, Landroidx/appcompat/widget/Toolbar;->H(II)V

    .line 9
    .line 10
    .line 11
    :cond_0
    return-void
.end method

.method public onMenuItemClick(Landroid/view/MenuItem;)Z
    .locals 1

    .line 1
    const-string v0, "item"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkb/l;->i(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Li1/b;->c:Ljb/l;

    .line 7
    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    invoke-interface {v0, p1}, Ljb/l;->k(Ljava/lang/Object;)Ljava/lang/Object;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    check-cast p1, Ljava/lang/Boolean;

    .line 15
    .line 16
    if-eqz p1, :cond_0

    .line 17
    .line 18
    invoke-virtual {p1}, Ljava/lang/Boolean;->booleanValue()Z

    .line 19
    .line 20
    .line 21
    move-result p1

    .line 22
    goto :goto_0

    .line 23
    :cond_0
    const/4 p1, 0x0

    .line 24
    :goto_0
    return p1
.end method

.method public final p(I)V
    .locals 1

    .line 1
    iput p1, p0, Li1/b;->n:I

    .line 2
    .line 3
    iget-object v0, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 4
    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {v0}, Landroidx/appcompat/widget/Toolbar;->getMenu()Landroid/view/Menu;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    if-eqz v0, :cond_0

    .line 12
    .line 13
    invoke-interface {v0}, Landroid/view/Menu;->clear()V

    .line 14
    .line 15
    .line 16
    :cond_0
    if-eqz p1, :cond_2

    .line 17
    .line 18
    iget-object v0, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 19
    .line 20
    if-eqz v0, :cond_1

    .line 21
    .line 22
    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/Toolbar;->x(I)V

    .line 23
    .line 24
    .line 25
    :cond_1
    iget-object p1, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 26
    .line 27
    if-eqz p1, :cond_3

    .line 28
    .line 29
    invoke-virtual {p1, p0}, Landroidx/appcompat/widget/Toolbar;->setOnMenuItemClickListener(Landroidx/appcompat/widget/Toolbar$f;)V

    .line 30
    .line 31
    .line 32
    goto :goto_0

    .line 33
    :cond_2
    iget-object p1, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 34
    .line 35
    if-eqz p1, :cond_3

    .line 36
    .line 37
    const/4 v0, 0x0

    .line 38
    invoke-virtual {p1, v0}, Landroidx/appcompat/widget/Toolbar;->setOnMenuItemClickListener(Landroidx/appcompat/widget/Toolbar$f;)V

    .line 39
    .line 40
    .line 41
    :cond_3
    :goto_0
    return-void
.end method

.method public final q(I)V
    .locals 1

    .line 1
    iput p1, p0, Li1/b;->l:I

    .line 2
    .line 3
    iget-object v0, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 4
    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/Toolbar;->setPopupTheme(I)V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method

.method public final r(Ljava/lang/String;)V
    .locals 1

    .line 1
    iput-object p1, p0, Li1/b;->i:Ljava/lang/String;

    .line 2
    .line 3
    iget-object v0, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 4
    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/Toolbar;->setSubtitle(Ljava/lang/CharSequence;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method

.method public final s(I)V
    .locals 1

    .line 1
    iput p1, p0, Li1/b;->k:I

    .line 2
    .line 3
    iget-object v0, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 4
    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/Toolbar;->setSubtitleTextColor(I)V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method

.method public final t(Ljava/lang/String;)V
    .locals 1

    .line 1
    iput-object p1, p0, Li1/b;->h:Ljava/lang/String;

    .line 2
    .line 3
    iget-object v0, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 4
    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/Toolbar;->setTitle(Ljava/lang/CharSequence;)V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method

.method public final u(I)V
    .locals 1

    .line 1
    iput p1, p0, Li1/b;->j:I

    .line 2
    .line 3
    iget-object v0, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 4
    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    invoke-virtual {v0, p1}, Landroidx/appcompat/widget/Toolbar;->setTitleTextColor(I)V

    .line 8
    .line 9
    .line 10
    :cond_0
    return-void
.end method

.method public final v(Landroidx/appcompat/widget/Toolbar;)V
    .locals 0

    .line 1
    iput-object p1, p0, Li1/b;->e:Landroidx/appcompat/widget/Toolbar;

    .line 2
    .line 3
    return-void
.end method
