.class public Lg0/a;
.super Lg0/d;
.source "FastOutLinearInInterpolator.java"


# static fields
.field private static final c:[F


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const/16 v0, 0xc9

    .line 2
    .line 3
    new-array v0, v0, [F

    .line 4
    .line 5
    fill-array-data v0, :array_0

    .line 6
    .line 7
    .line 8
    sput-object v0, Lg0/a;->c:[F

    .line 9
    .line 10
    return-void

    .line 11
    :array_0
    .array-data 4
        0x0
        0x38d1b717    # 1.0E-4f
        0x3951b717    # 2.0E-4f
        0x3a03126f    # 5.0E-4f
        0x3a51b717    # 8.0E-4f
        0x3aaa64c3    # 0.0013f
        0x3aebedfa    # 0.0018f
        0x3b1d4952    # 0.0024f
        0x3b51b717    # 0.0032f
        0x3b83126f    # 0.004f
        0x3ba0902e    # 0.0049f
        0x3bc154ca    # 0.0059f
        0x3be21965    # 0.0069f
        0x3c04b5dd    # 0.0081f
        0x3c185f07    # 0.0093f
        0x3c2dab9f    # 0.0106f
        0x3c449ba6    # 0.012f
        0x3c5d2f1b    # 0.0135f
        0x3c7765fe    # 0.0151f
        0x3c88ce70    # 0.0167f
        0x3c96bb99    # 0.0184f
        0x3ca4a8c1    # 0.0201f
        0x3cb43958    # 0.022f
        0x3cc3c9ef    # 0.0239f
        0x3cd42c3d    # 0.0259f
        0x3ce48e8a    # 0.0279f
        0x3cf5c28f    # 0.03f
        0x3d03e426    # 0.0322f
        0x3d0d4fdf    # 0.0345f
        0x3d16bb99    # 0.0368f
        0x3d202752    # 0.0391f
        0x3d2a64c3    # 0.0416f
        0x3d34a234    # 0.0441f
        0x3d3edfa4    # 0.0466f
        0x3d4985f0    # 0.0492f
        0x3d549518    # 0.0519f
        0x3d600d1b    # 0.0547f
        0x3d6b1c43    # 0.0574f
        0x3d76fd22    # 0.0603f
        0x3d816f00    # 0.0632f
        0x3d8793de    # 0.0662f
        0x3d8db8bb    # 0.0692f
        0x3d93dd98    # 0.0722f
        0x3d9a6b51    # 0.0754f
        0x3da0c49c    # 0.0785f
        0x3da75254    # 0.0817f
        0x3dae147b    # 0.085f
        0x3db50b0f    # 0.0884f
        0x3dbbcd36    # 0.0917f
        0x3dc2f838    # 0.0952f
        0x3dc9eecc    # 0.0986f
        0x3dd119ce    # 0.1021f
        0x3dd8793e    # 0.1057f
        0x3ddfd8ae    # 0.1093f
        0x3de76c8b    # 0.113f
        0x3def0069    # 0.1167f
        0x3df6c8b4    # 0.1205f
        0x3dfe9100    # 0.1243f
        0x3e032ca5    # 0.1281f
        0x3e072b02    # 0.132f
        0x3e0b295f    # 0.1359f
        0x3e0f41f2    # 0.1399f
        0x3e135a86    # 0.1439f
        0x3e178d50    # 0.148f
        0x3e1bc01a    # 0.1521f
        0x3e1ff2e5    # 0.1562f
        0x3e243fe6    # 0.1604f
        0x3e28a71e    # 0.1647f
        0x3e2cf41f    # 0.1689f
        0x3e315b57    # 0.1732f
        0x3e35dcc6    # 0.1776f
        0x3e3a5e35    # 0.182f
        0x3e3edfa4    # 0.1864f
        0x3e437b4a    # 0.1909f
        0x3e4816f0    # 0.1954f
        0x3e4cb296    # 0.1999f
        0x3e516873    # 0.2045f
        0x3e561e4f    # 0.2091f
        0x3e5aee63    # 0.2138f
        0x3e5fa440    # 0.2184f
        0x3e648e8a    # 0.2232f
        0x3e695e9e    # 0.2279f
        0x3e6e48e9    # 0.2327f
        0x3e734d6a    # 0.2376f
        0x3e7837b5    # 0.2424f
        0x3e7d3c36    # 0.2473f
        0x3e812d77    # 0.2523f
        0x3e83afb8    # 0.2572f
        0x3e863f14    # 0.2622f
        0x3e88db8c    # 0.2673f
        0x3e8b6ae8    # 0.2723f
        0x3e8e075f    # 0.2774f
        0x3e90b0f2    # 0.2826f
        0x3e934d6a    # 0.2877f
        0x3e95f6fd    # 0.2929f
        0x3e98adac    # 0.2982f
        0x3e9b573f    # 0.3034f
        0x3e9e0ded    # 0.3087f
        0x3ea0d1b7    # 0.3141f
        0x3ea38866    # 0.3194f
        0x3ea64c30    # 0.3248f
        0x3ea90ff9    # 0.3302f
        0x3eabe0df    # 0.3357f
        0x3eaeb1c4    # 0.3412f
        0x3eb182aa    # 0.3467f
        0x3eb4538f    # 0.3522f
        0x3eb73190    # 0.3578f
        0x3eba0f91    # 0.3634f
        0x3ebced91    # 0.369f
        0x3ebfd8ae    # 0.3747f
        0x3ec2c3ca    # 0.3804f
        0x3ec5aee6    # 0.3861f
        0x3ec89a02    # 0.3918f
        0x3ecb923a    # 0.3976f
        0x3ece8a72    # 0.4034f
        0x3ed182aa    # 0.4092f
        0x3ed487fd    # 0.4151f
        0x3ed78d50    # 0.421f
        0x3eda92a3    # 0.4269f
        0x3edda512    # 0.4329f
        0x3ee0aa65    # 0.4388f
        0x3ee3bcd3    # 0.4448f
        0x3ee6cf42    # 0.4508f
        0x3ee9eecc    # 0.4569f
        0x3eed0e56    # 0.463f
        0x3ef02de0    # 0.4691f
        0x3ef34d6a    # 0.4752f
        0x3ef67a10    # 0.4814f
        0x3ef9a6b5    # 0.4876f
        0x3efcd35b    # 0.4938f
        0x3f000000    # 0.5f
        0x3f019ce0    # 0.5063f
        0x3f0339c1    # 0.5126f
        0x3f04d6a1    # 0.5189f
        0x3f067382    # 0.5252f
        0x3f0816f0    # 0.5316f
        0x3f09ba5e    # 0.538f
        0x3f0b5dcc    # 0.5444f
        0x3f0d013b    # 0.5508f
        0x3f0eab36    # 0.5573f
        0x3f105532    # 0.5638f
        0x3f11ff2e    # 0.5703f
        0x3f13a92a    # 0.5768f
        0x3f1559b4    # 0.5834f
        0x3f170a3d    # 0.59f
        0x3f18bac7    # 0.5966f
        0x3f1a71de    # 0.6033f
        0x3f1c2268    # 0.6099f
        0x3f1dd97f    # 0.6166f
        0x3f1f9097    # 0.6233f
        0x3f214e3c    # 0.6301f
        0x3f230be1    # 0.6369f
        0x3f24c2f8    # 0.6436f
        0x3f26872b    # 0.6505f
        0x3f2844d0    # 0.6573f
        0x3f2a0903    # 0.6642f
        0x3f2bc6a8    # 0.671f
        0x3f2d9168    # 0.678f
        0x3f2f559b    # 0.6849f
        0x3f31205c    # 0.6919f
        0x3f32e48f    # 0.6988f
        0x3f34b5dd    # 0.7059f
        0x3f36809d    # 0.7129f
        0x3f384b5e    # 0.7199f
        0x3f3a1cac    # 0.727f
        0x3f3bedfa    # 0.7341f
        0x3f3dc5d6    # 0.7413f
        0x3f3f9724    # 0.7484f
        0x3f416f00    # 0.7556f
        0x3f4346dc    # 0.7628f
        0x3f451eb8    # 0.77f
        0x3f46fd22    # 0.7773f
        0x3f48db8c    # 0.7846f
        0x3f4ab9f5    # 0.7919f
        0x3f4c985f    # 0.7992f
        0x3f4e7d56    # 0.8066f
        0x3f50624e    # 0.814f
        0x3f524745    # 0.8214f
        0x3f542c3d    # 0.8288f
        0x3f5617c2    # 0.8363f
        0x3f57fcb9    # 0.8437f
        0x3f59eecc    # 0.8513f
        0x3f5bda51    # 0.8588f
        0x3f5dcc64    # 0.8664f
        0x3f5fbe77    # 0.874f
        0x3f61b08a    # 0.8816f
        0x3f63a29c    # 0.8892f
        0x3f659b3d    # 0.8969f
        0x3f6793de    # 0.9046f
        0x3f69930c    # 0.9124f
        0x3f6b8bac    # 0.9201f
        0x3f6d9168    # 0.928f
        0x3f6f9097    # 0.9358f
        0x3f719653    # 0.9437f
        0x3f739c0f    # 0.9516f
        0x3f75a1cb    # 0.9595f
        0x3f77ae14    # 0.9675f
        0x3f79ba5e    # 0.9755f
        0x3f7bcd36    # 0.9836f
        0x3f7de69b    # 0.9918f
        0x3f800000    # 1.0f
    .end array-data
.end method

.method public constructor <init>()V
    .locals 1

    .line 1
    sget-object v0, Lg0/a;->c:[F

    .line 2
    .line 3
    invoke-direct {p0, v0}, Lg0/d;-><init>([F)V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public bridge synthetic getInterpolation(F)F
    .locals 0

    .line 1
    invoke-super {p0, p1}, Lg0/d;->getInterpolation(F)F

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method
