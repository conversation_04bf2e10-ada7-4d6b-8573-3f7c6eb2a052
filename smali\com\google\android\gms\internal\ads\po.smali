.class final Lcom/google/android/gms/internal/ads/po;
.super Ljava/lang/Object;
.source "com.google.android.gms:play-services-ads@@21.3.0"

# interfaces
.implements Lcom/google/android/gms/ads/internal/zzf;


# instance fields
.field final synthetic a:Lcom/google/android/gms/internal/ads/zzchh;

.field final synthetic b:Lcom/google/android/gms/internal/ads/zzfdw;

.field final synthetic c:Lcom/google/android/gms/internal/ads/zzfdk;

.field final synthetic d:Lcom/google/android/gms/internal/ads/zzems;

.field final synthetic e:Lcom/google/android/gms/internal/ads/zzemn;


# direct methods
.method constructor <init>(Lcom/google/android/gms/internal/ads/zzemn;Lcom/google/android/gms/internal/ads/zzchh;Lcom/google/android/gms/internal/ads/zzfdw;Lcom/google/android/gms/internal/ads/zzfdk;Lcom/google/android/gms/internal/ads/zzems;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/google/android/gms/internal/ads/po;->e:Lcom/google/android/gms/internal/ads/zzemn;

    .line 2
    .line 3
    iput-object p2, p0, Lcom/google/android/gms/internal/ads/po;->a:Lcom/google/android/gms/internal/ads/zzchh;

    .line 4
    .line 5
    iput-object p3, p0, Lcom/google/android/gms/internal/ads/po;->b:Lcom/google/android/gms/internal/ads/zzfdw;

    .line 6
    .line 7
    iput-object p4, p0, Lcom/google/android/gms/internal/ads/po;->c:Lcom/google/android/gms/internal/ads/zzfdk;

    .line 8
    .line 9
    iput-object p5, p0, Lcom/google/android/gms/internal/ads/po;->d:Lcom/google/android/gms/internal/ads/zzems;

    .line 10
    .line 11
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 12
    .line 13
    .line 14
    return-void
.end method


# virtual methods
.method public final zza(Landroid/view/View;)V
    .locals 5

    .line 1
    iget-object v0, p0, Lcom/google/android/gms/internal/ads/po;->a:Lcom/google/android/gms/internal/ads/zzchh;

    .line 2
    .line 3
    iget-object v1, p0, Lcom/google/android/gms/internal/ads/po;->e:Lcom/google/android/gms/internal/ads/zzemn;

    .line 4
    .line 5
    invoke-static {v1}, Lcom/google/android/gms/internal/ads/zzemn;->zzd(Lcom/google/android/gms/internal/ads/zzemn;)Lcom/google/android/gms/internal/ads/zzemw;

    .line 6
    .line 7
    .line 8
    move-result-object v1

    .line 9
    iget-object v2, p0, Lcom/google/android/gms/internal/ads/po;->b:Lcom/google/android/gms/internal/ads/zzfdw;

    .line 10
    .line 11
    iget-object v3, p0, Lcom/google/android/gms/internal/ads/po;->c:Lcom/google/android/gms/internal/ads/zzfdk;

    .line 12
    .line 13
    iget-object v4, p0, Lcom/google/android/gms/internal/ads/po;->d:Lcom/google/android/gms/internal/ads/zzems;

    .line 14
    .line 15
    invoke-virtual {v1, v2, v3, p1, v4}, Lcom/google/android/gms/internal/ads/zzemw;->zza(Lcom/google/android/gms/internal/ads/zzfdw;Lcom/google/android/gms/internal/ads/zzfdk;Landroid/view/View;Lcom/google/android/gms/internal/ads/zzems;)Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    invoke-virtual {v0, p1}, Lcom/google/android/gms/internal/ads/zzchh;->zzd(Ljava/lang/Object;)Z

    .line 20
    .line 21
    .line 22
    return-void
.end method

.method public final zzb()V
    .locals 0

    .line 1
    return-void
.end method

.method public final zzc()V
    .locals 0

    .line 1
    return-void
.end method
