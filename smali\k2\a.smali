.class public Lk2/a;
.super Ljava/lang/Object;
.source "DrawableCrossFadeFactory.java"

# interfaces
.implements Lk2/e;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lk2/a$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lk2/e<",
        "Landroid/graphics/drawable/Drawable;",
        ">;"
    }
.end annotation


# instance fields
.field private final a:I

.field private final b:Z

.field private c:Lk2/b;


# direct methods
.method protected constructor <init>(IZ)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput p1, p0, Lk2/a;->a:I

    .line 5
    .line 6
    iput-boolean p2, p0, Lk2/a;->b:Z

    .line 7
    .line 8
    return-void
.end method

.method private b()Lk2/d;
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()",
            "Lk2/d<",
            "Landroid/graphics/drawable/Drawable;",
            ">;"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Lk2/a;->c:Lk2/b;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    new-instance v0, Lk2/b;

    .line 6
    .line 7
    iget v1, p0, Lk2/a;->a:I

    .line 8
    .line 9
    iget-boolean v2, p0, Lk2/a;->b:Z

    .line 10
    .line 11
    invoke-direct {v0, v1, v2}, Lk2/b;-><init>(IZ)V

    .line 12
    .line 13
    .line 14
    iput-object v0, p0, Lk2/a;->c:Lk2/b;

    .line 15
    .line 16
    :cond_0
    iget-object v0, p0, Lk2/a;->c:Lk2/b;

    .line 17
    .line 18
    return-object v0
.end method


# virtual methods
.method public a(Lq1/a;Z)Lk2/d;
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lq1/a;",
            "Z)",
            "Lk2/d<",
            "Landroid/graphics/drawable/Drawable;",
            ">;"
        }
    .end annotation

    .line 1
    sget-object p2, Lq1/a;->e:Lq1/a;

    .line 2
    .line 3
    if-ne p1, p2, :cond_0

    .line 4
    .line 5
    invoke-static {}, Lk2/c;->b()Lk2/d;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    goto :goto_0

    .line 10
    :cond_0
    invoke-direct {p0}, Lk2/a;->b()Lk2/d;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    :goto_0
    return-object p1
.end method
