.class public abstract Lf3/o$a;
.super Ljava/lang/Object;
.source "TransportContext.java"


# annotations
.annotation build Lcom/google/auto/value/AutoValue$Builder;
.end annotation

.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lf3/o;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x409
    name = "a"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract a()Lf3/o;
.end method

.method public abstract b(Ljava/lang/String;)Lf3/o$a;
.end method

.method public abstract c([B)Lf3/o$a;
.end method

.method public abstract d(Ld3/d;)Lf3/o$a;
.end method
