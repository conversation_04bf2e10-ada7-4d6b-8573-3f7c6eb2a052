.class public final Lm1/c;
.super Ljava/lang/Object;
.source "com.android.billingclient:billing-ktx@@5.0.0"


# direct methods
.method public static final a(Lcom/android/billingclient/api/a;Lm1/a;Lbb/d;)Ljava/lang/Object;
    .locals 2
    .param p0    # Lcom/android/billingclient/api/a;
        .annotation build Landroidx/annotation/RecentlyNonNull;
        .end annotation
    .end param
    .param p1    # Lm1/a;
        .annotation build Landroidx/annotation/RecentlyNonNull;
        .end annotation
    .end param
    .param p2    # Lbb/d;
        .annotation build Landroidx/annotation/RecentlyNonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/RecentlyNonNull;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/android/billingclient/api/a;",
            "Lm1/a;",
            "Lbb/d<",
            "-",
            "Lcom/android/billingclient/api/d;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    invoke-static {v0, v1, v0}, Lub/w;->b(Lub/r1;ILjava/lang/Object;)Lub/u;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lm1/c$a;

    .line 8
    .line 9
    invoke-direct {v1, v0}, Lm1/c$a;-><init>(Lub/u;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0, p1, v1}, Lcom/android/billingclient/api/a;->a(Lm1/a;Lm1/b;)V

    .line 13
    .line 14
    .line 15
    invoke-interface {v0, p2}, Lub/p0;->n0(Lbb/d;)Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    return-object p0
.end method

.method public static final b(Lcom/android/billingclient/api/a;Lcom/android/billingclient/api/f;Lbb/d;)Ljava/lang/Object;
    .locals 2
    .param p0    # Lcom/android/billingclient/api/a;
        .annotation build Landroidx/annotation/RecentlyNonNull;
        .end annotation
    .end param
    .param p1    # Lcom/android/billingclient/api/f;
        .annotation build Landroidx/annotation/RecentlyNonNull;
        .end annotation
    .end param
    .param p2    # Lbb/d;
        .annotation build Landroidx/annotation/RecentlyNonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/RecentlyNonNull;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/android/billingclient/api/a;",
            "Lcom/android/billingclient/api/f;",
            "Lbb/d<",
            "-",
            "Lm1/h;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    invoke-static {v0, v1, v0}, Lub/w;->b(Lub/r1;ILjava/lang/Object;)Lub/u;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lm1/c$b;

    .line 8
    .line 9
    invoke-direct {v1, v0}, Lm1/c$b;-><init>(Lub/u;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0, p1, v1}, Lcom/android/billingclient/api/a;->e(Lcom/android/billingclient/api/f;Lm1/g;)V

    .line 13
    .line 14
    .line 15
    invoke-interface {v0, p2}, Lub/p0;->n0(Lbb/d;)Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    return-object p0
.end method

.method public static final c(Lcom/android/billingclient/api/a;Lm1/l;Lbb/d;)Ljava/lang/Object;
    .locals 2
    .param p0    # Lcom/android/billingclient/api/a;
        .annotation build Landroidx/annotation/RecentlyNonNull;
        .end annotation
    .end param
    .param p1    # Lm1/l;
        .annotation build Landroidx/annotation/RecentlyNonNull;
        .end annotation
    .end param
    .param p2    # Lbb/d;
        .annotation build Landroidx/annotation/RecentlyNonNull;
        .end annotation
    .end param
    .annotation build Landroidx/annotation/RecentlyNonNull;
    .end annotation

    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/android/billingclient/api/a;",
            "Lm1/l;",
            "Lbb/d<",
            "-",
            "Lm1/j;",
            ">;)",
            "Ljava/lang/Object;"
        }
    .end annotation

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    invoke-static {v0, v1, v0}, Lub/w;->b(Lub/r1;ILjava/lang/Object;)Lub/u;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    new-instance v1, Lm1/c$c;

    .line 8
    .line 9
    invoke-direct {v1, v0}, Lm1/c$c;-><init>(Lub/u;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0, p1, v1}, Lcom/android/billingclient/api/a;->f(Lm1/l;Lm1/i;)V

    .line 13
    .line 14
    .line 15
    invoke-interface {v0, p2}, Lub/p0;->n0(Lbb/d;)Ljava/lang/Object;

    .line 16
    .line 17
    .line 18
    move-result-object p0

    .line 19
    return-object p0
.end method
