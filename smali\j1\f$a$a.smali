.class Lj1/f$a$a;
.super Ljava/lang/Object;
.source "MaterialDialog.java"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lj1/f$a;->onGlobalLayout()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic a:I

.field final synthetic b:Lj1/f$a;


# direct methods
.method constructor <init>(Lj1/f$a;I)V
    .locals 0

    .line 1
    iput-object p1, p0, Lj1/f$a$a;->b:Lj1/f$a;

    .line 2
    .line 3
    iput p2, p0, Lj1/f$a$a;->a:I

    .line 4
    .line 5
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 6
    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public run()V
    .locals 2

    .line 1
    iget-object v0, p0, Lj1/f$a$a;->b:Lj1/f$a;

    .line 2
    .line 3
    iget-object v0, v0, Lj1/f$a;->a:Lj1/f;

    .line 4
    .line 5
    iget-object v0, v0, Lj1/f;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 6
    .line 7
    invoke-virtual {v0}, Landroid/view/View;->requestFocus()Z

    .line 8
    .line 9
    .line 10
    iget-object v0, p0, Lj1/f$a$a;->b:Lj1/f$a;

    .line 11
    .line 12
    iget-object v0, v0, Lj1/f$a;->a:Lj1/f;

    .line 13
    .line 14
    iget-object v0, v0, Lj1/f;->c:Lj1/f$d;

    .line 15
    .line 16
    iget-object v0, v0, Lj1/f$d;->T:Landroidx/recyclerview/widget/RecyclerView$p;

    .line 17
    .line 18
    iget v1, p0, Lj1/f$a$a;->a:I

    .line 19
    .line 20
    invoke-virtual {v0, v1}, Landroidx/recyclerview/widget/RecyclerView$p;->y1(I)V

    .line 21
    .line 22
    .line 23
    return-void
.end method
