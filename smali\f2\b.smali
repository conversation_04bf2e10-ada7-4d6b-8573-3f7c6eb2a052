.class Lf2/b;
.super Ljava/lang/Object;
.source "ApplicationLifecycle.java"

# interfaces
.implements Lf2/h;


# direct methods
.method constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Lf2/i;)V
    .locals 0

    .line 1
    return-void
.end method

.method public b(Lf2/i;)V
    .locals 0

    .line 1
    invoke-interface {p1}, Lf2/i;->onStart()V

    .line 2
    .line 3
    .line 4
    return-void
.end method
