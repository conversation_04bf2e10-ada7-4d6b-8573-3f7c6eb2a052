.class public interface abstract Li2/g;
.super Ljava/lang/Object;
.source "ResourceCallback.java"


# virtual methods
.method public abstract a(Lt1/c;Lq1/a;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lt1/c<",
            "*>;",
            "Lq1/a;",
            ")V"
        }
    .end annotation
.end method

.method public abstract b(Lcom/bumptech/glide/load/engine/GlideException;)V
.end method

.method public abstract g()Ljava/lang/Object;
.end method
