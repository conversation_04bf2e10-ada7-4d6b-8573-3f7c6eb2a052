.class final Lcom/google/android/gms/internal/ads/p1;
.super Ljava/lang/Object;
.source "com.google.android.gms:play-services-ads@@21.3.0"


# instance fields
.field private final a:Ljava/util/UUID;

.field private final b:[B


# direct methods
.method public constructor <init>(Ljava/util/UUID;I[B)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lcom/google/android/gms/internal/ads/p1;->a:Ljava/util/UUID;

    .line 5
    .line 6
    iput-object p3, p0, Lcom/google/android/gms/internal/ads/p1;->b:[B

    .line 7
    .line 8
    return-void
.end method

.method static bridge synthetic a(Lcom/google/android/gms/internal/ads/p1;)Ljava/util/UUID;
    .locals 0

    .line 1
    iget-object p0, p0, Lcom/google/android/gms/internal/ads/p1;->a:Ljava/util/UUID;

    .line 2
    .line 3
    return-object p0
.end method
