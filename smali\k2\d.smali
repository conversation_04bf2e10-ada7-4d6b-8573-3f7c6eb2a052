.class public interface abstract Lk2/d;
.super Ljava/lang/Object;
.source "Transition.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lk2/d$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<R:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# virtual methods
.method public abstract a(Ljava/lang/Object;Lk2/d$a;)Z
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TR;",
            "Lk2/d$a;",
            ")Z"
        }
    .end annotation
.end method
