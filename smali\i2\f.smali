.class public Li2/f;
.super Li2/a;
.source "RequestOptions.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Li2/a<",
        "Li2/f;",
        ">;"
    }
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Li2/a;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static g0(Ljava/lang/Class;)Li2/f;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/lang/Class<",
            "*>;)",
            "Li2/f;"
        }
    .end annotation

    .line 1
    new-instance v0, Li2/f;

    .line 2
    .line 3
    invoke-direct {v0}, Li2/f;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0, p0}, Li2/a;->f(Ljava/lang/Class;)Li2/a;

    .line 7
    .line 8
    .line 9
    move-result-object p0

    .line 10
    check-cast p0, Li2/f;

    .line 11
    .line 12
    return-object p0
.end method

.method public static h0(Lt1/a;)Li2/f;
    .locals 1

    .line 1
    new-instance v0, Li2/f;

    .line 2
    .line 3
    invoke-direct {v0}, Li2/f;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0, p0}, Li2/a;->g(Lt1/a;)Li2/a;

    .line 7
    .line 8
    .line 9
    move-result-object p0

    .line 10
    check-cast p0, Li2/f;

    .line 11
    .line 12
    return-object p0
.end method

.method public static i0(Lq1/e;)Li2/f;
    .locals 1

    .line 1
    new-instance v0, Li2/f;

    .line 2
    .line 3
    invoke-direct {v0}, Li2/f;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0, p0}, Li2/a;->Y(Lq1/e;)Li2/a;

    .line 7
    .line 8
    .line 9
    move-result-object p0

    .line 10
    check-cast p0, Li2/f;

    .line 11
    .line 12
    return-object p0
.end method
