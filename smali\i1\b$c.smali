.class final Li1/b$c;
.super Ljava/lang/Object;
.source "MaterialCab.kt"

# interfaces
.implements Landroid/view/View$OnClickListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Li1/b;->j(Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = null
.end annotation


# static fields
.field public static final a:Li1/b$c;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Li1/b$c;

    .line 2
    .line 3
    invoke-direct {v0}, Li1/b$c;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, Li1/b$c;->a:Li1/b$c;

    .line 7
    .line 8
    return-void
.end method

.method constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final onClick(Landroid/view/View;)V
    .locals 0

    .line 1
    sget-object p1, Li1/b;->t:Li1/b$a;

    .line 2
    .line 3
    invoke-virtual {p1}, Li1/b$a;->a()Z

    .line 4
    .line 5
    .line 6
    return-void
.end method
