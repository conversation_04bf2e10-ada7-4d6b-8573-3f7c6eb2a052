.class public abstract Lf3/n$a;
.super Ljava/lang/Object;
.source "SendRequest.java"


# annotations
.annotation build Lcom/google/auto/value/AutoValue$Builder;
.end annotation

.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lf3/n;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x409
    name = "a"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public abstract a()Lf3/n;
.end method

.method abstract b(Ld3/b;)Lf3/n$a;
.end method

.method abstract c(Ld3/c;)Lf3/n$a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ld3/c<",
            "*>;)",
            "Lf3/n$a;"
        }
    .end annotation
.end method

.method abstract d(Ld3/e;)Lf3/n$a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ld3/e<",
            "*[B>;)",
            "Lf3/n$a;"
        }
    .end annotation
.end method

.method public abstract e(Lf3/o;)Lf3/n$a;
.end method

.method public abstract f(Ljava/lang/String;)Lf3/n$a;
.end method
