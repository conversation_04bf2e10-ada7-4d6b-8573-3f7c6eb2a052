.class public abstract Lm3/h;
.super Ljava/lang/Object;
.source "SchedulingModule.java"


# direct methods
.method static a(Landroid/content/Context;Lo3/d;Ln3/f;Lq3/a;)Ln3/x;
    .locals 0

    .line 1
    new-instance p3, Ln3/d;

    .line 2
    .line 3
    invoke-direct {p3, p0, p1, p2}, Ln3/d;-><init>(Landroid/content/Context;Lo3/d;Ln3/f;)V

    .line 4
    .line 5
    .line 6
    return-object p3
.end method
