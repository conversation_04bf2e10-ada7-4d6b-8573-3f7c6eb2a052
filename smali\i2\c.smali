.class public interface abstract Li2/c;
.super Ljava/lang/Object;
.source "Request.java"


# virtual methods
.method public abstract c()Z
.end method

.method public abstract clear()V
.end method

.method public abstract d(Li2/c;)Z
.end method

.method public abstract f()Z
.end method

.method public abstract h()V
.end method

.method public abstract isRunning()Z
.end method

.method public abstract j()Z
.end method

.method public abstract pause()V
.end method
