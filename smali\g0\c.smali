.class public Lg0/c;
.super Lg0/d;
.source "LinearOutSlowInInterpolator.java"


# static fields
.field private static final c:[F


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const/16 v0, 0xc9

    .line 2
    .line 3
    new-array v0, v0, [F

    .line 4
    .line 5
    fill-array-data v0, :array_0

    .line 6
    .line 7
    .line 8
    sput-object v0, Lg0/c;->c:[F

    .line 9
    .line 10
    return-void

    .line 11
    :array_0
    .array-data 4
        0x0
        0x3cb5dcc6    # 0.0222f
        0x3d2dab9f    # 0.0424f
        0x3d7b15b5    # 0.0613f
        0x3da2680a    # 0.0793f
        0x3dc5d639    # 0.0966f
        0x3de7d567    # 0.1132f
        0x3e046738    # 0.1293f
        0x3e1460aa    # 0.1449f
        0x3e23d70a    # 0.16f
        0x3e32e48f    # 0.1747f
        0x3e418937    # 0.189f
        0x3e4fc505    # 0.2029f
        0x3e5db22d    # 0.2165f
        0x3e6b50b1    # 0.2298f
        0x3e78a090    # 0.2428f
        0x3e82d0e5    # 0.2555f
        0x3e89374c    # 0.268f
        0x3e8f7660    # 0.2802f
        0x3e958e22    # 0.2921f
        0x3e9b8bac    # 0.3038f
        0x3ea16f00    # 0.3153f
        0x3ea7381d    # 0.3266f
        0x3eace704    # 0.3377f
        0x3eb27bb3    # 0.3486f
        0x3eb7e910    # 0.3592f
        0x3ebd4952    # 0.3697f
        0x3ec29c78    # 0.3801f
        0x3ec7c84b    # 0.3902f
        0x3ecce704    # 0.4002f
        0x3ed1eb85    # 0.41f
        0x3ed6d5d0    # 0.4196f
        0x3edbb2ff    # 0.4291f
        0x3ee08312    # 0.4385f
        0x3ee538ef    # 0.4477f
        0x3ee9d495    # 0.4567f
        0x3eee6320    # 0.4656f
        0x3ef2e48f    # 0.4744f
        0x3ef758e2    # 0.4831f
        0x3efbb2ff    # 0.4916f
        0x3f000000    # 0.5f
        0x3f021ff3    # 0.5083f
        0x3f0432ca    # 0.5164f
        0x3f0645a2    # 0.5245f
        0x3f084b5e    # 0.5324f
        0x3f0a4a8c    # 0.5402f
        0x3f0c432d    # 0.5479f
        0x3f0e353f    # 0.5555f
        0x3f101a37    # 0.5629f
        0x3f11ff2e    # 0.5703f
        0x3f13dd98    # 0.5776f
        0x3f15aee6    # 0.5847f
        0x3f178034    # 0.5918f
        0x3f194af5    # 0.5988f
        0x3f1b0f28    # 0.6057f
        0x3f1cc63f    # 0.6124f
        0x3f1e7d56    # 0.6191f
        0x3f202de0    # 0.6257f
        0x3f21d7dc    # 0.6322f
        0x3f2381d8    # 0.6387f
        0x3f251eb8    # 0.645f
        0x3f26b50b    # 0.6512f
        0x3f284b5e    # 0.6574f
        0x3f29db23    # 0.6635f
        0x3f2b645a    # 0.6695f
        0x3f2ce704    # 0.6754f
        0x3f2e6320    # 0.6812f
        0x3f2fdf3b    # 0.687f
        0x3f3154ca    # 0.6927f
        0x3f32c3ca    # 0.6983f
        0x3f342c3d    # 0.7038f
        0x3f3594af    # 0.7093f
        0x3f36f694    # 0.7147f
        0x3f3851ec    # 0.72f
        0x3f39a6b5    # 0.7252f
        0x3f3afb7f    # 0.7304f
        0x3f3c49ba    # 0.7355f
        0x3f3d97f6    # 0.7406f
        0x3f3ed917    # 0.7455f
        0x3f401a37    # 0.7504f
        0x3f415b57    # 0.7553f
        0x3f428f5c    # 0.76f
        0x3f43c361    # 0.7647f
        0x3f44f766    # 0.7694f
        0x3f4624dd    # 0.774f
        0x3f474bc7    # 0.7785f
        0x3f486c22    # 0.7829f
        0x3f498c7e    # 0.7873f
        0x3f4aacda    # 0.7917f
        0x3f4bc01a    # 0.7959f
        0x3f4cd9e8    # 0.8002f
        0x3f4de69b    # 0.8043f
        0x3f4ef34d    # 0.8084f
        0x3f500000    # 0.8125f
        0x3f510625    # 0.8165f
        0x3f5205bc    # 0.8204f
        0x3f530553    # 0.8243f
        0x3f53fe5d    # 0.8281f
        0x3f54f766    # 0.8319f
        0x3f55e9e2    # 0.8356f
        0x3f56d5d0    # 0.8392f
        0x3f57c84b    # 0.8429f
        0x3f58adac    # 0.8464f
        0x3f59930c    # 0.8499f
        0x3f5a786c    # 0.8534f
        0x3f5b573f    # 0.8568f
        0x3f5c2f83    # 0.8601f
        0x3f5d07c8    # 0.8634f
        0x3f5de00d    # 0.8667f
        0x3f5eb1c4    # 0.8699f
        0x3f5f837b    # 0.8731f
        0x3f604ea5    # 0.8762f
        0x3f611340    # 0.8792f
        0x3f61de6a    # 0.8823f
        0x3f629c78    # 0.8852f
        0x3f636113    # 0.8882f
        0x3f641893    # 0.891f
        0x3f64d6a1    # 0.8939f
        0x3f658e22    # 0.8967f
        0x3f663f14    # 0.8994f
        0x3f66f007    # 0.9021f
        0x3f67a0f9    # 0.9048f
        0x3f684b5e    # 0.9074f
        0x3f68f5c3    # 0.91f
        0x3f69999a    # 0.9125f
        0x3f6a3d71    # 0.915f
        0x3f6adaba    # 0.9174f
        0x3f6b7803    # 0.9198f
        0x3f6c154d    # 0.9222f
        0x3f6cac08    # 0.9245f
        0x3f6d42c4    # 0.9268f
        0x3f6dd2f2    # 0.929f
        0x3f6e6320    # 0.9312f
        0x3f6ef34d    # 0.9334f
        0x3f6f7cee    # 0.9355f
        0x3f70068e    # 0.9376f
        0x3f7089a0    # 0.9396f
        0x3f710cb3    # 0.9416f
        0x3f718fc5    # 0.9436f
        0x3f720c4a    # 0.9455f
        0x3f7288ce    # 0.9474f
        0x3f72fec5    # 0.9492f
        0x3f7374bc    # 0.951f
        0x3f73eab3    # 0.9528f
        0x3f745a1d    # 0.9545f
        0x3f74c986    # 0.9562f
        0x3f7538ef    # 0.9579f
        0x3f75a1cb    # 0.9595f
        0x3f760aa6    # 0.9611f
        0x3f767382    # 0.9627f
        0x3f76d5d0    # 0.9642f
        0x3f77381d    # 0.9657f
        0x3f779a6b    # 0.9672f
        0x3f77f62b    # 0.9686f
        0x3f7851ec    # 0.97f
        0x3f78a71e    # 0.9713f
        0x3f78fc50    # 0.9726f
        0x3f795183    # 0.9739f
        0x3f79a6b5    # 0.9752f
        0x3f79f55a    # 0.9764f
        0x3f7a43fe    # 0.9776f
        0x3f7a8c15    # 0.9787f
        0x3f7ad42c    # 0.9798f
        0x3f7b1c43    # 0.9809f
        0x3f7b645a    # 0.982f
        0x3f7ba5e3    # 0.983f
        0x3f7be76d    # 0.984f
        0x3f7c2268    # 0.9849f
        0x3f7c63f1    # 0.9859f
        0x3f7c9eed    # 0.9868f
        0x3f7cd35b    # 0.9876f
        0x3f7d0e56    # 0.9885f
        0x3f7d42c4    # 0.9893f
        0x3f7d70a4    # 0.99f
        0x3f7da512    # 0.9908f
        0x3f7dd2f2    # 0.9915f
        0x3f7e00d2    # 0.9922f
        0x3f7e2824    # 0.9928f
        0x3f7e4f76    # 0.9934f
        0x3f7e76c9    # 0.994f
        0x3f7e9e1b    # 0.9946f
        0x3f7ebee0    # 0.9951f
        0x3f7edfa4    # 0.9956f
        0x3f7f0069    # 0.9961f
        0x3f7f212d    # 0.9966f
        0x3f7f3b64    # 0.997f
        0x3f7f559b    # 0.9974f
        0x3f7f6944    # 0.9977f
        0x3f7f837b    # 0.9981f
        0x3f7f9724    # 0.9984f
        0x3f7faace    # 0.9987f
        0x3f7fb7e9    # 0.9989f
        0x3f7fcb92    # 0.9992f
        0x3f7fd8ae    # 0.9994f
        0x3f7fdf3b    # 0.9995f
        0x3f7fec57    # 0.9997f
        0x3f7ff2e5    # 0.9998f
        0x3f7ff972    # 0.9999f
        0x3f7ff972    # 0.9999f
        0x3f800000    # 1.0f
        0x3f800000    # 1.0f
    .end array-data
.end method

.method public constructor <init>()V
    .locals 1

    .line 1
    sget-object v0, Lg0/c;->c:[F

    .line 2
    .line 3
    invoke-direct {p0, v0}, Lg0/d;-><init>([F)V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public bridge synthetic getInterpolation(F)F
    .locals 0

    .line 1
    invoke-super {p0, p1}, Lg0/d;->getInterpolation(F)F

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method
