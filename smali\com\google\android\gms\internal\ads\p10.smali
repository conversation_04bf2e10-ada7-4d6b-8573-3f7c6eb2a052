.class final Lcom/google/android/gms/internal/ads/p10;
.super Ljava/lang/Object;
.source "com.google.android.gms:play-services-ads@@21.3.0"

# interfaces
.implements Lcom/google/android/gms/internal/ads/j10;


# instance fields
.field private final a:Lcom/google/android/gms/internal/ads/zzgpc;

.field private final b:Lcom/google/android/gms/internal/ads/zzgpc;


# direct methods
.method private constructor <init>([B[B)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    invoke-static {p1}, Lcom/google/android/gms/internal/ads/zzgpc;->zza([B)Lcom/google/android/gms/internal/ads/zzgpc;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    iput-object p1, p0, Lcom/google/android/gms/internal/ads/p10;->a:Lcom/google/android/gms/internal/ads/zzgpc;

    .line 9
    .line 10
    invoke-static {p2}, Lcom/google/android/gms/internal/ads/zzgpc;->zza([B)Lcom/google/android/gms/internal/ads/zzgpc;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    iput-object p1, p0, Lcom/google/android/gms/internal/ads/p10;->b:Lcom/google/android/gms/internal/ads/zzgpc;

    .line 15
    .line 16
    return-void
.end method

.method static a([B[BI)Lcom/google/android/gms/internal/ads/p10;
    .locals 2
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/security/GeneralSecurityException;
        }
    .end annotation

    .line 1
    invoke-static {p2}, Lcom/google/android/gms/internal/ads/zzgoe;->zzk(I)Ljava/security/spec/ECParameterSpec;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    const/4 v1, 0x1

    .line 6
    invoke-static {v0, v1, p1}, Lcom/google/android/gms/internal/ads/zzgoe;->zzj(Ljava/security/spec/ECParameterSpec;I[B)Ljava/security/interfaces/ECPublicKey;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-static {p2, p0}, Lcom/google/android/gms/internal/ads/zzgoe;->zzh(I[B)Ljava/security/interfaces/ECPrivateKey;

    .line 11
    .line 12
    .line 13
    move-result-object p2

    .line 14
    invoke-static {v0, p2}, Lcom/google/android/gms/internal/ads/zzgoe;->zze(Ljava/security/interfaces/ECPublicKey;Ljava/security/interfaces/ECPrivateKey;)V

    .line 15
    .line 16
    .line 17
    new-instance p2, Lcom/google/android/gms/internal/ads/p10;

    .line 18
    .line 19
    invoke-direct {p2, p0, p1}, Lcom/google/android/gms/internal/ads/p10;-><init>([B[B)V

    .line 20
    .line 21
    .line 22
    return-object p2
.end method
