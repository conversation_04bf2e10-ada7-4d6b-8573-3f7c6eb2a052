.class final Lf3/a$c;
.super Ljava/lang/Object;
.source "AutoProtoEncoderDoNotUseEncoder.java"

# interfaces
.implements Lh5/c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lf3/a;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x1a
    name = "c"
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Lh5/c<",
        "Lj3/c;",
        ">;"
    }
.end annotation


# static fields
.field static final a:Lf3/a$c;

.field private static final b:Lh5/b;

.field private static final c:Lh5/b;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Lf3/a$c;

    .line 2
    .line 3
    invoke-direct {v0}, Lf3/a$c;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, Lf3/a$c;->a:Lf3/a$c;

    .line 7
    .line 8
    const-string v0, "eventsDroppedCount"

    .line 9
    .line 10
    invoke-static {v0}, Lh5/b;->a(Ljava/lang/String;)Lh5/b$b;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-static {}, Lk5/a;->b()Lk5/a;

    .line 15
    .line 16
    .line 17
    move-result-object v1

    .line 18
    const/4 v2, 0x1

    .line 19
    invoke-virtual {v1, v2}, Lk5/a;->c(I)Lk5/a;

    .line 20
    .line 21
    .line 22
    move-result-object v1

    .line 23
    invoke-virtual {v1}, Lk5/a;->a()Lk5/d;

    .line 24
    .line 25
    .line 26
    move-result-object v1

    .line 27
    invoke-virtual {v0, v1}, Lh5/b$b;->b(Ljava/lang/annotation/Annotation;)Lh5/b$b;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    invoke-virtual {v0}, Lh5/b$b;->a()Lh5/b;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    sput-object v0, Lf3/a$c;->b:Lh5/b;

    .line 36
    .line 37
    const-string v0, "reason"

    .line 38
    .line 39
    invoke-static {v0}, Lh5/b;->a(Ljava/lang/String;)Lh5/b$b;

    .line 40
    .line 41
    .line 42
    move-result-object v0

    .line 43
    invoke-static {}, Lk5/a;->b()Lk5/a;

    .line 44
    .line 45
    .line 46
    move-result-object v1

    .line 47
    const/4 v2, 0x3

    .line 48
    invoke-virtual {v1, v2}, Lk5/a;->c(I)Lk5/a;

    .line 49
    .line 50
    .line 51
    move-result-object v1

    .line 52
    invoke-virtual {v1}, Lk5/a;->a()Lk5/d;

    .line 53
    .line 54
    .line 55
    move-result-object v1

    .line 56
    invoke-virtual {v0, v1}, Lh5/b$b;->b(Ljava/lang/annotation/Annotation;)Lh5/b$b;

    .line 57
    .line 58
    .line 59
    move-result-object v0

    .line 60
    invoke-virtual {v0}, Lh5/b$b;->a()Lh5/b;

    .line 61
    .line 62
    .line 63
    move-result-object v0

    .line 64
    sput-object v0, Lf3/a$c;->c:Lh5/b;

    .line 65
    .line 66
    return-void
.end method

.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public bridge synthetic a(Ljava/lang/Object;Ljava/lang/Object;)V
    .locals 0
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    check-cast p1, Lj3/c;

    .line 2
    .line 3
    check-cast p2, Lh5/d;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, Lf3/a$c;->b(Lj3/c;Lh5/d;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public b(Lj3/c;Lh5/d;)V
    .locals 3
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 1
    sget-object v0, Lf3/a$c;->b:Lh5/b;

    .line 2
    .line 3
    invoke-virtual {p1}, Lj3/c;->a()J

    .line 4
    .line 5
    .line 6
    move-result-wide v1

    .line 7
    invoke-interface {p2, v0, v1, v2}, Lh5/d;->b(Lh5/b;J)Lh5/d;

    .line 8
    .line 9
    .line 10
    sget-object v0, Lf3/a$c;->c:Lh5/b;

    .line 11
    .line 12
    invoke-virtual {p1}, Lj3/c;->b()Lj3/c$b;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    invoke-interface {p2, v0, p1}, Lh5/d;->d(Lh5/b;Ljava/lang/Object;)Lh5/d;

    .line 17
    .line 18
    .line 19
    return-void
.end method
