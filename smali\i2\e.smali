.class public interface abstract Li2/e;
.super Ljava/lang/Object;
.source "RequestListener.java"


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<R:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;"
    }
.end annotation


# virtual methods
.method public abstract a(Ljava/lang/Object;Ljava/lang/Object;Lj2/i;Lq1/a;Z)Z
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TR;",
            "Ljava/lang/Object;",
            "Lj2/i<",
            "TR;>;",
            "Lq1/a;",
            "Z)Z"
        }
    .end annotation
.end method

.method public abstract b(Lcom/bumptech/glide/load/engine/GlideException;Ljava/lang/Object;Lj2/i;Z)Z
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/bumptech/glide/load/engine/GlideException;",
            "Ljava/lang/Object;",
            "Lj2/i<",
            "TR;>;Z)Z"
        }
    .end annotation
.end method
