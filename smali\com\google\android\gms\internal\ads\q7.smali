.class Lcom/google/android/gms/internal/ads/q7;
.super Ljava/lang/Object;
.source "com.google.android.gms:play-services-ads@@21.3.0"


# static fields
.field public static final A:I

.field public static final A0:I

.field public static final B:I

.field public static final B0:I

.field public static final C:I

.field public static final C0:I

.field public static final D:I

.field public static final D0:I

.field public static final E:I

.field public static final E0:I

.field public static final F:I

.field public static final F0:I

.field public static final G:I

.field public static final G0:I

.field public static final H:I

.field public static final H0:I

.field public static final I:I

.field public static final I0:I

.field public static final J:I

.field public static final J0:I

.field public static final K:I

.field public static final K0:I

.field public static final L:I

.field public static final L0:I

.field public static final M:I

.field public static final M0:I

.field public static final N:I

.field public static final N0:I

.field public static final O:I

.field public static final O0:I

.field public static final P:I

.field public static final Q:I

.field public static final R:I

.field public static final S:I

.field public static final T:I

.field public static final U:I

.field public static final V:I

.field public static final W:I

.field public static final X:I

.field public static final Y:I

.field public static final Z:I

.field public static final a0:I

.field public static final b:I

.field public static final b0:I

.field public static final c:I

.field public static final c0:I

.field public static final d:I

.field public static final d0:I

.field public static final e:I

.field public static final e0:I

.field public static final f:I

.field public static final f0:I

.field public static final g:I

.field public static final g0:I

.field public static final h:I

.field public static final h0:I

.field public static final i:I

.field public static final i0:I

.field public static final j:I

.field public static final j0:I

.field public static final k:I

.field public static final k0:I

.field public static final l:I

.field public static final l0:I

.field public static final m:I

.field public static final m0:I

.field public static final n:I

.field public static final n0:I

.field public static final o:I

.field public static final o0:I

.field public static final p:I

.field public static final p0:I

.field public static final q:I

.field public static final q0:I

.field public static final r:I

.field public static final r0:I

.field public static final s:I

.field public static final s0:I

.field public static final t:I

.field public static final t0:I

.field public static final u:I

.field public static final u0:I

.field public static final v:I

.field public static final v0:I

.field public static final w:I

.field public static final w0:I

.field public static final x:I

.field public static final x0:I

.field public static final y:I

.field public static final y0:I

.field public static final z:I

.field public static final z0:I


# instance fields
.field public final a:I


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const-string v0, "ftyp"

    .line 2
    .line 3
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    sput v0, Lcom/google/android/gms/internal/ads/q7;->b:I

    .line 8
    .line 9
    const-string v0, "avc1"

    .line 10
    .line 11
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 12
    .line 13
    .line 14
    move-result v0

    .line 15
    sput v0, Lcom/google/android/gms/internal/ads/q7;->c:I

    .line 16
    .line 17
    const-string v0, "avc3"

    .line 18
    .line 19
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    sput v0, Lcom/google/android/gms/internal/ads/q7;->d:I

    .line 24
    .line 25
    const-string v0, "hvc1"

    .line 26
    .line 27
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 28
    .line 29
    .line 30
    move-result v0

    .line 31
    sput v0, Lcom/google/android/gms/internal/ads/q7;->e:I

    .line 32
    .line 33
    const-string v0, "hev1"

    .line 34
    .line 35
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 36
    .line 37
    .line 38
    move-result v0

    .line 39
    sput v0, Lcom/google/android/gms/internal/ads/q7;->f:I

    .line 40
    .line 41
    const-string v0, "s263"

    .line 42
    .line 43
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 44
    .line 45
    .line 46
    move-result v0

    .line 47
    sput v0, Lcom/google/android/gms/internal/ads/q7;->g:I

    .line 48
    .line 49
    const-string v0, "d263"

    .line 50
    .line 51
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 52
    .line 53
    .line 54
    move-result v0

    .line 55
    sput v0, Lcom/google/android/gms/internal/ads/q7;->h:I

    .line 56
    .line 57
    const-string v0, "mdat"

    .line 58
    .line 59
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 60
    .line 61
    .line 62
    move-result v0

    .line 63
    sput v0, Lcom/google/android/gms/internal/ads/q7;->i:I

    .line 64
    .line 65
    const-string v0, "mp4a"

    .line 66
    .line 67
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 68
    .line 69
    .line 70
    move-result v0

    .line 71
    sput v0, Lcom/google/android/gms/internal/ads/q7;->j:I

    .line 72
    .line 73
    const-string v0, ".mp3"

    .line 74
    .line 75
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 76
    .line 77
    .line 78
    move-result v0

    .line 79
    sput v0, Lcom/google/android/gms/internal/ads/q7;->k:I

    .line 80
    .line 81
    const-string v0, "wave"

    .line 82
    .line 83
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 84
    .line 85
    .line 86
    move-result v0

    .line 87
    sput v0, Lcom/google/android/gms/internal/ads/q7;->l:I

    .line 88
    .line 89
    const-string v0, "lpcm"

    .line 90
    .line 91
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 92
    .line 93
    .line 94
    move-result v0

    .line 95
    sput v0, Lcom/google/android/gms/internal/ads/q7;->m:I

    .line 96
    .line 97
    const-string v0, "sowt"

    .line 98
    .line 99
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 100
    .line 101
    .line 102
    move-result v0

    .line 103
    sput v0, Lcom/google/android/gms/internal/ads/q7;->n:I

    .line 104
    .line 105
    const-string v0, "ac-3"

    .line 106
    .line 107
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 108
    .line 109
    .line 110
    move-result v0

    .line 111
    sput v0, Lcom/google/android/gms/internal/ads/q7;->o:I

    .line 112
    .line 113
    const-string v0, "dac3"

    .line 114
    .line 115
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 116
    .line 117
    .line 118
    move-result v0

    .line 119
    sput v0, Lcom/google/android/gms/internal/ads/q7;->p:I

    .line 120
    .line 121
    const-string v0, "ec-3"

    .line 122
    .line 123
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 124
    .line 125
    .line 126
    move-result v0

    .line 127
    sput v0, Lcom/google/android/gms/internal/ads/q7;->q:I

    .line 128
    .line 129
    const-string v0, "dec3"

    .line 130
    .line 131
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 132
    .line 133
    .line 134
    move-result v0

    .line 135
    sput v0, Lcom/google/android/gms/internal/ads/q7;->r:I

    .line 136
    .line 137
    const-string v0, "dtsc"

    .line 138
    .line 139
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 140
    .line 141
    .line 142
    move-result v0

    .line 143
    sput v0, Lcom/google/android/gms/internal/ads/q7;->s:I

    .line 144
    .line 145
    const-string v0, "dtsh"

    .line 146
    .line 147
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 148
    .line 149
    .line 150
    move-result v0

    .line 151
    sput v0, Lcom/google/android/gms/internal/ads/q7;->t:I

    .line 152
    .line 153
    const-string v0, "dtsl"

    .line 154
    .line 155
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 156
    .line 157
    .line 158
    move-result v0

    .line 159
    sput v0, Lcom/google/android/gms/internal/ads/q7;->u:I

    .line 160
    .line 161
    const-string v0, "dtse"

    .line 162
    .line 163
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 164
    .line 165
    .line 166
    move-result v0

    .line 167
    sput v0, Lcom/google/android/gms/internal/ads/q7;->v:I

    .line 168
    .line 169
    const-string v0, "ddts"

    .line 170
    .line 171
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 172
    .line 173
    .line 174
    move-result v0

    .line 175
    sput v0, Lcom/google/android/gms/internal/ads/q7;->w:I

    .line 176
    .line 177
    const-string v0, "tfdt"

    .line 178
    .line 179
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 180
    .line 181
    .line 182
    move-result v0

    .line 183
    sput v0, Lcom/google/android/gms/internal/ads/q7;->x:I

    .line 184
    .line 185
    const-string v0, "tfhd"

    .line 186
    .line 187
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 188
    .line 189
    .line 190
    move-result v0

    .line 191
    sput v0, Lcom/google/android/gms/internal/ads/q7;->y:I

    .line 192
    .line 193
    const-string v0, "trex"

    .line 194
    .line 195
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 196
    .line 197
    .line 198
    move-result v0

    .line 199
    sput v0, Lcom/google/android/gms/internal/ads/q7;->z:I

    .line 200
    .line 201
    const-string v0, "trun"

    .line 202
    .line 203
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 204
    .line 205
    .line 206
    move-result v0

    .line 207
    sput v0, Lcom/google/android/gms/internal/ads/q7;->A:I

    .line 208
    .line 209
    const-string v0, "sidx"

    .line 210
    .line 211
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 212
    .line 213
    .line 214
    move-result v0

    .line 215
    sput v0, Lcom/google/android/gms/internal/ads/q7;->B:I

    .line 216
    .line 217
    const-string v0, "moov"

    .line 218
    .line 219
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 220
    .line 221
    .line 222
    move-result v0

    .line 223
    sput v0, Lcom/google/android/gms/internal/ads/q7;->C:I

    .line 224
    .line 225
    const-string v0, "mvhd"

    .line 226
    .line 227
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 228
    .line 229
    .line 230
    move-result v0

    .line 231
    sput v0, Lcom/google/android/gms/internal/ads/q7;->D:I

    .line 232
    .line 233
    const-string v0, "trak"

    .line 234
    .line 235
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 236
    .line 237
    .line 238
    move-result v0

    .line 239
    sput v0, Lcom/google/android/gms/internal/ads/q7;->E:I

    .line 240
    .line 241
    const-string v0, "mdia"

    .line 242
    .line 243
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 244
    .line 245
    .line 246
    move-result v0

    .line 247
    sput v0, Lcom/google/android/gms/internal/ads/q7;->F:I

    .line 248
    .line 249
    const-string v0, "minf"

    .line 250
    .line 251
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 252
    .line 253
    .line 254
    move-result v0

    .line 255
    sput v0, Lcom/google/android/gms/internal/ads/q7;->G:I

    .line 256
    .line 257
    const-string v0, "stbl"

    .line 258
    .line 259
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 260
    .line 261
    .line 262
    move-result v0

    .line 263
    sput v0, Lcom/google/android/gms/internal/ads/q7;->H:I

    .line 264
    .line 265
    const-string v0, "avcC"

    .line 266
    .line 267
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 268
    .line 269
    .line 270
    move-result v0

    .line 271
    sput v0, Lcom/google/android/gms/internal/ads/q7;->I:I

    .line 272
    .line 273
    const-string v0, "hvcC"

    .line 274
    .line 275
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 276
    .line 277
    .line 278
    move-result v0

    .line 279
    sput v0, Lcom/google/android/gms/internal/ads/q7;->J:I

    .line 280
    .line 281
    const-string v0, "esds"

    .line 282
    .line 283
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 284
    .line 285
    .line 286
    move-result v0

    .line 287
    sput v0, Lcom/google/android/gms/internal/ads/q7;->K:I

    .line 288
    .line 289
    const-string v0, "moof"

    .line 290
    .line 291
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 292
    .line 293
    .line 294
    move-result v0

    .line 295
    sput v0, Lcom/google/android/gms/internal/ads/q7;->L:I

    .line 296
    .line 297
    const-string v0, "traf"

    .line 298
    .line 299
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 300
    .line 301
    .line 302
    move-result v0

    .line 303
    sput v0, Lcom/google/android/gms/internal/ads/q7;->M:I

    .line 304
    .line 305
    const-string v0, "mvex"

    .line 306
    .line 307
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 308
    .line 309
    .line 310
    move-result v0

    .line 311
    sput v0, Lcom/google/android/gms/internal/ads/q7;->N:I

    .line 312
    .line 313
    const-string v0, "mehd"

    .line 314
    .line 315
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 316
    .line 317
    .line 318
    move-result v0

    .line 319
    sput v0, Lcom/google/android/gms/internal/ads/q7;->O:I

    .line 320
    .line 321
    const-string v0, "tkhd"

    .line 322
    .line 323
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 324
    .line 325
    .line 326
    move-result v0

    .line 327
    sput v0, Lcom/google/android/gms/internal/ads/q7;->P:I

    .line 328
    .line 329
    const-string v0, "edts"

    .line 330
    .line 331
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 332
    .line 333
    .line 334
    move-result v0

    .line 335
    sput v0, Lcom/google/android/gms/internal/ads/q7;->Q:I

    .line 336
    .line 337
    const-string v0, "elst"

    .line 338
    .line 339
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 340
    .line 341
    .line 342
    move-result v0

    .line 343
    sput v0, Lcom/google/android/gms/internal/ads/q7;->R:I

    .line 344
    .line 345
    const-string v0, "mdhd"

    .line 346
    .line 347
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 348
    .line 349
    .line 350
    move-result v0

    .line 351
    sput v0, Lcom/google/android/gms/internal/ads/q7;->S:I

    .line 352
    .line 353
    const-string v0, "hdlr"

    .line 354
    .line 355
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 356
    .line 357
    .line 358
    move-result v0

    .line 359
    sput v0, Lcom/google/android/gms/internal/ads/q7;->T:I

    .line 360
    .line 361
    const-string v0, "stsd"

    .line 362
    .line 363
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 364
    .line 365
    .line 366
    move-result v0

    .line 367
    sput v0, Lcom/google/android/gms/internal/ads/q7;->U:I

    .line 368
    .line 369
    const-string v0, "pssh"

    .line 370
    .line 371
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 372
    .line 373
    .line 374
    move-result v0

    .line 375
    sput v0, Lcom/google/android/gms/internal/ads/q7;->V:I

    .line 376
    .line 377
    const-string v0, "sinf"

    .line 378
    .line 379
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 380
    .line 381
    .line 382
    move-result v0

    .line 383
    sput v0, Lcom/google/android/gms/internal/ads/q7;->W:I

    .line 384
    .line 385
    const-string v0, "schm"

    .line 386
    .line 387
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 388
    .line 389
    .line 390
    move-result v0

    .line 391
    sput v0, Lcom/google/android/gms/internal/ads/q7;->X:I

    .line 392
    .line 393
    const-string v0, "schi"

    .line 394
    .line 395
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 396
    .line 397
    .line 398
    move-result v0

    .line 399
    sput v0, Lcom/google/android/gms/internal/ads/q7;->Y:I

    .line 400
    .line 401
    const-string v0, "tenc"

    .line 402
    .line 403
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 404
    .line 405
    .line 406
    move-result v0

    .line 407
    sput v0, Lcom/google/android/gms/internal/ads/q7;->Z:I

    .line 408
    .line 409
    const-string v0, "encv"

    .line 410
    .line 411
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 412
    .line 413
    .line 414
    move-result v0

    .line 415
    sput v0, Lcom/google/android/gms/internal/ads/q7;->a0:I

    .line 416
    .line 417
    const-string v0, "enca"

    .line 418
    .line 419
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 420
    .line 421
    .line 422
    move-result v0

    .line 423
    sput v0, Lcom/google/android/gms/internal/ads/q7;->b0:I

    .line 424
    .line 425
    const-string v0, "frma"

    .line 426
    .line 427
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 428
    .line 429
    .line 430
    move-result v0

    .line 431
    sput v0, Lcom/google/android/gms/internal/ads/q7;->c0:I

    .line 432
    .line 433
    const-string v0, "saiz"

    .line 434
    .line 435
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 436
    .line 437
    .line 438
    move-result v0

    .line 439
    sput v0, Lcom/google/android/gms/internal/ads/q7;->d0:I

    .line 440
    .line 441
    const-string v0, "saio"

    .line 442
    .line 443
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 444
    .line 445
    .line 446
    move-result v0

    .line 447
    sput v0, Lcom/google/android/gms/internal/ads/q7;->e0:I

    .line 448
    .line 449
    const-string v0, "sbgp"

    .line 450
    .line 451
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 452
    .line 453
    .line 454
    move-result v0

    .line 455
    sput v0, Lcom/google/android/gms/internal/ads/q7;->f0:I

    .line 456
    .line 457
    const-string v0, "sgpd"

    .line 458
    .line 459
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 460
    .line 461
    .line 462
    move-result v0

    .line 463
    sput v0, Lcom/google/android/gms/internal/ads/q7;->g0:I

    .line 464
    .line 465
    const-string v0, "uuid"

    .line 466
    .line 467
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 468
    .line 469
    .line 470
    move-result v0

    .line 471
    sput v0, Lcom/google/android/gms/internal/ads/q7;->h0:I

    .line 472
    .line 473
    const-string v0, "senc"

    .line 474
    .line 475
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 476
    .line 477
    .line 478
    move-result v0

    .line 479
    sput v0, Lcom/google/android/gms/internal/ads/q7;->i0:I

    .line 480
    .line 481
    const-string v0, "pasp"

    .line 482
    .line 483
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 484
    .line 485
    .line 486
    move-result v0

    .line 487
    sput v0, Lcom/google/android/gms/internal/ads/q7;->j0:I

    .line 488
    .line 489
    const-string v0, "TTML"

    .line 490
    .line 491
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 492
    .line 493
    .line 494
    move-result v0

    .line 495
    sput v0, Lcom/google/android/gms/internal/ads/q7;->k0:I

    .line 496
    .line 497
    const-string v0, "vmhd"

    .line 498
    .line 499
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 500
    .line 501
    .line 502
    const-string v0, "mp4v"

    .line 503
    .line 504
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 505
    .line 506
    .line 507
    move-result v0

    .line 508
    sput v0, Lcom/google/android/gms/internal/ads/q7;->l0:I

    .line 509
    .line 510
    const-string v0, "stts"

    .line 511
    .line 512
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 513
    .line 514
    .line 515
    move-result v0

    .line 516
    sput v0, Lcom/google/android/gms/internal/ads/q7;->m0:I

    .line 517
    .line 518
    const-string v0, "stss"

    .line 519
    .line 520
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 521
    .line 522
    .line 523
    move-result v0

    .line 524
    sput v0, Lcom/google/android/gms/internal/ads/q7;->n0:I

    .line 525
    .line 526
    const-string v0, "ctts"

    .line 527
    .line 528
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 529
    .line 530
    .line 531
    move-result v0

    .line 532
    sput v0, Lcom/google/android/gms/internal/ads/q7;->o0:I

    .line 533
    .line 534
    const-string v0, "stsc"

    .line 535
    .line 536
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 537
    .line 538
    .line 539
    move-result v0

    .line 540
    sput v0, Lcom/google/android/gms/internal/ads/q7;->p0:I

    .line 541
    .line 542
    const-string v0, "stsz"

    .line 543
    .line 544
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 545
    .line 546
    .line 547
    move-result v0

    .line 548
    sput v0, Lcom/google/android/gms/internal/ads/q7;->q0:I

    .line 549
    .line 550
    const-string v0, "stz2"

    .line 551
    .line 552
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 553
    .line 554
    .line 555
    move-result v0

    .line 556
    sput v0, Lcom/google/android/gms/internal/ads/q7;->r0:I

    .line 557
    .line 558
    const-string v0, "stco"

    .line 559
    .line 560
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 561
    .line 562
    .line 563
    move-result v0

    .line 564
    sput v0, Lcom/google/android/gms/internal/ads/q7;->s0:I

    .line 565
    .line 566
    const-string v0, "co64"

    .line 567
    .line 568
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 569
    .line 570
    .line 571
    move-result v0

    .line 572
    sput v0, Lcom/google/android/gms/internal/ads/q7;->t0:I

    .line 573
    .line 574
    const-string v0, "tx3g"

    .line 575
    .line 576
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 577
    .line 578
    .line 579
    move-result v0

    .line 580
    sput v0, Lcom/google/android/gms/internal/ads/q7;->u0:I

    .line 581
    .line 582
    const-string v0, "wvtt"

    .line 583
    .line 584
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 585
    .line 586
    .line 587
    move-result v0

    .line 588
    sput v0, Lcom/google/android/gms/internal/ads/q7;->v0:I

    .line 589
    .line 590
    const-string v0, "stpp"

    .line 591
    .line 592
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 593
    .line 594
    .line 595
    move-result v0

    .line 596
    sput v0, Lcom/google/android/gms/internal/ads/q7;->w0:I

    .line 597
    .line 598
    const-string v0, "c608"

    .line 599
    .line 600
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 601
    .line 602
    .line 603
    move-result v0

    .line 604
    sput v0, Lcom/google/android/gms/internal/ads/q7;->x0:I

    .line 605
    .line 606
    const-string v0, "samr"

    .line 607
    .line 608
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 609
    .line 610
    .line 611
    move-result v0

    .line 612
    sput v0, Lcom/google/android/gms/internal/ads/q7;->y0:I

    .line 613
    .line 614
    const-string v0, "sawb"

    .line 615
    .line 616
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 617
    .line 618
    .line 619
    move-result v0

    .line 620
    sput v0, Lcom/google/android/gms/internal/ads/q7;->z0:I

    .line 621
    .line 622
    const-string v0, "udta"

    .line 623
    .line 624
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 625
    .line 626
    .line 627
    move-result v0

    .line 628
    sput v0, Lcom/google/android/gms/internal/ads/q7;->A0:I

    .line 629
    .line 630
    const-string v0, "meta"

    .line 631
    .line 632
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 633
    .line 634
    .line 635
    move-result v0

    .line 636
    sput v0, Lcom/google/android/gms/internal/ads/q7;->B0:I

    .line 637
    .line 638
    const-string v0, "ilst"

    .line 639
    .line 640
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 641
    .line 642
    .line 643
    move-result v0

    .line 644
    sput v0, Lcom/google/android/gms/internal/ads/q7;->C0:I

    .line 645
    .line 646
    const-string v0, "mean"

    .line 647
    .line 648
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 649
    .line 650
    .line 651
    move-result v0

    .line 652
    sput v0, Lcom/google/android/gms/internal/ads/q7;->D0:I

    .line 653
    .line 654
    const-string v0, "name"

    .line 655
    .line 656
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 657
    .line 658
    .line 659
    move-result v0

    .line 660
    sput v0, Lcom/google/android/gms/internal/ads/q7;->E0:I

    .line 661
    .line 662
    const-string v0, "data"

    .line 663
    .line 664
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 665
    .line 666
    .line 667
    move-result v0

    .line 668
    sput v0, Lcom/google/android/gms/internal/ads/q7;->F0:I

    .line 669
    .line 670
    const-string v0, "emsg"

    .line 671
    .line 672
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 673
    .line 674
    .line 675
    move-result v0

    .line 676
    sput v0, Lcom/google/android/gms/internal/ads/q7;->G0:I

    .line 677
    .line 678
    const-string v0, "st3d"

    .line 679
    .line 680
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 681
    .line 682
    .line 683
    move-result v0

    .line 684
    sput v0, Lcom/google/android/gms/internal/ads/q7;->H0:I

    .line 685
    .line 686
    const-string v0, "sv3d"

    .line 687
    .line 688
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 689
    .line 690
    .line 691
    move-result v0

    .line 692
    sput v0, Lcom/google/android/gms/internal/ads/q7;->I0:I

    .line 693
    .line 694
    const-string v0, "proj"

    .line 695
    .line 696
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 697
    .line 698
    .line 699
    move-result v0

    .line 700
    sput v0, Lcom/google/android/gms/internal/ads/q7;->J0:I

    .line 701
    .line 702
    const-string v0, "vp08"

    .line 703
    .line 704
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 705
    .line 706
    .line 707
    move-result v0

    .line 708
    sput v0, Lcom/google/android/gms/internal/ads/q7;->K0:I

    .line 709
    .line 710
    const-string v0, "vp09"

    .line 711
    .line 712
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 713
    .line 714
    .line 715
    move-result v0

    .line 716
    sput v0, Lcom/google/android/gms/internal/ads/q7;->L0:I

    .line 717
    .line 718
    const-string v0, "vpcC"

    .line 719
    .line 720
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 721
    .line 722
    .line 723
    move-result v0

    .line 724
    sput v0, Lcom/google/android/gms/internal/ads/q7;->M0:I

    .line 725
    .line 726
    const-string v0, "camm"

    .line 727
    .line 728
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 729
    .line 730
    .line 731
    move-result v0

    .line 732
    sput v0, Lcom/google/android/gms/internal/ads/q7;->N0:I

    .line 733
    .line 734
    const-string v0, "alac"

    .line 735
    .line 736
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbar;->zzg(Ljava/lang/String;)I

    .line 737
    .line 738
    .line 739
    move-result v0

    .line 740
    sput v0, Lcom/google/android/gms/internal/ads/q7;->O0:I

    .line 741
    .line 742
    return-void
.end method

.method public constructor <init>(I)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput p1, p0, Lcom/google/android/gms/internal/ads/q7;->a:I

    .line 5
    .line 6
    return-void
.end method

.method public static a(I)I
    .locals 1

    .line 1
    const v0, 0xffffff

    .line 2
    .line 3
    .line 4
    and-int/2addr p0, v0

    .line 5
    return p0
.end method

.method public static b(I)I
    .locals 0

    .line 1
    shr-int/lit8 p0, p0, 0x18

    .line 2
    .line 3
    and-int/lit16 p0, p0, 0xff

    .line 4
    .line 5
    return p0
.end method

.method public static c(I)Ljava/lang/String;
    .locals 2

    .line 1
    new-instance v0, Ljava/lang/StringBuilder;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 4
    .line 5
    .line 6
    shr-int/lit8 v1, p0, 0x18

    .line 7
    .line 8
    and-int/lit16 v1, v1, 0xff

    .line 9
    .line 10
    int-to-char v1, v1

    .line 11
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 12
    .line 13
    .line 14
    shr-int/lit8 v1, p0, 0x10

    .line 15
    .line 16
    and-int/lit16 v1, v1, 0xff

    .line 17
    .line 18
    int-to-char v1, v1

    .line 19
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 20
    .line 21
    .line 22
    shr-int/lit8 v1, p0, 0x8

    .line 23
    .line 24
    and-int/lit16 v1, v1, 0xff

    .line 25
    .line 26
    int-to-char v1, v1

    .line 27
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 28
    .line 29
    .line 30
    and-int/lit16 p0, p0, 0xff

    .line 31
    .line 32
    int-to-char p0, p0

    .line 33
    invoke-virtual {v0, p0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 34
    .line 35
    .line 36
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object p0

    .line 40
    return-object p0
.end method


# virtual methods
.method public toString()Ljava/lang/String;
    .locals 1

    .line 1
    iget v0, p0, Lcom/google/android/gms/internal/ads/q7;->a:I

    .line 2
    .line 3
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/q7;->c(I)Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method
