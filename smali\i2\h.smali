.class public final Li2/h;
.super Ljava/lang/Object;
.source "SingleRequest.java"

# interfaces
.implements Li2/c;
.implements Lj2/h;
.implements Li2/g;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Li2/h$a;
    }
.end annotation

.annotation system Ldalvik/annotation/Signature;
    value = {
        "<R:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Li2/c;",
        "Lj2/h;",
        "Li2/g;"
    }
.end annotation


# static fields
.field private static final D:Z


# instance fields
.field private A:I

.field private B:Z

.field private C:Ljava/lang/RuntimeException;

.field private final a:Ljava/lang/String;

.field private final b:Ln2/c;

.field private final c:Ljava/lang/Object;

.field private final d:Li2/e;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Li2/e<",
            "TR;>;"
        }
    .end annotation
.end field

.field private final e:Li2/d;

.field private final f:Landroid/content/Context;

.field private final g:Lcom/bumptech/glide/e;

.field private final h:Ljava/lang/Object;

.field private final i:Ljava/lang/Class;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/lang/Class<",
            "TR;>;"
        }
    .end annotation
.end field

.field private final j:Li2/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Li2/a<",
            "*>;"
        }
    .end annotation
.end field

.field private final k:I

.field private final l:I

.field private final m:Lcom/bumptech/glide/g;

.field private final n:Lj2/i;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lj2/i<",
            "TR;>;"
        }
    .end annotation
.end field

.field private final o:Ljava/util/List;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/List<",
            "Li2/e<",
            "TR;>;>;"
        }
    .end annotation
.end field

.field private final p:Lk2/e;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lk2/e<",
            "-TR;>;"
        }
    .end annotation
.end field

.field private final q:Ljava/util/concurrent/Executor;

.field private r:Lt1/c;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lt1/c<",
            "TR;>;"
        }
    .end annotation
.end field

.field private s:Lcom/bumptech/glide/load/engine/j$d;

.field private t:J

.field private volatile u:Lcom/bumptech/glide/load/engine/j;

.field private v:Li2/h$a;

.field private w:Landroid/graphics/drawable/Drawable;

.field private x:Landroid/graphics/drawable/Drawable;

.field private y:Landroid/graphics/drawable/Drawable;

.field private z:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    const-string v0, "Request"

    .line 2
    .line 3
    const/4 v1, 0x2

    .line 4
    invoke-static {v0, v1}, Landroid/util/Log;->isLoggable(Ljava/lang/String;I)Z

    .line 5
    .line 6
    .line 7
    move-result v0

    .line 8
    sput-boolean v0, Li2/h;->D:Z

    .line 9
    .line 10
    return-void
.end method

.method private constructor <init>(Landroid/content/Context;Lcom/bumptech/glide/e;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Class;Li2/a;IILcom/bumptech/glide/g;Lj2/i;Li2/e;Ljava/util/List;Li2/d;Lcom/bumptech/glide/load/engine/j;Lk2/e;Ljava/util/concurrent/Executor;)V
    .locals 3
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Landroid/content/Context;",
            "Lcom/bumptech/glide/e;",
            "Ljava/lang/Object;",
            "Ljava/lang/Object;",
            "Ljava/lang/Class<",
            "TR;>;",
            "Li2/a<",
            "*>;II",
            "Lcom/bumptech/glide/g;",
            "Lj2/i<",
            "TR;>;",
            "Li2/e<",
            "TR;>;",
            "Ljava/util/List<",
            "Li2/e<",
            "TR;>;>;",
            "Li2/d;",
            "Lcom/bumptech/glide/load/engine/j;",
            "Lk2/e<",
            "-TR;>;",
            "Ljava/util/concurrent/Executor;",
            ")V"
        }
    .end annotation

    .line 1
    move-object v0, p0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    .line 4
    .line 5
    sget-boolean v1, Li2/h;->D:Z

    .line 6
    .line 7
    if-eqz v1, :cond_0

    .line 8
    .line 9
    invoke-super {p0}, Ljava/lang/Object;->hashCode()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    invoke-static {v1}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 v1, 0x0

    .line 19
    :goto_0
    iput-object v1, v0, Li2/h;->a:Ljava/lang/String;

    .line 20
    .line 21
    invoke-static {}, Ln2/c;->a()Ln2/c;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    iput-object v1, v0, Li2/h;->b:Ln2/c;

    .line 26
    .line 27
    move-object v1, p3

    .line 28
    iput-object v1, v0, Li2/h;->c:Ljava/lang/Object;

    .line 29
    .line 30
    move-object v1, p1

    .line 31
    iput-object v1, v0, Li2/h;->f:Landroid/content/Context;

    .line 32
    .line 33
    move-object v1, p2

    .line 34
    iput-object v1, v0, Li2/h;->g:Lcom/bumptech/glide/e;

    .line 35
    .line 36
    move-object v2, p4

    .line 37
    iput-object v2, v0, Li2/h;->h:Ljava/lang/Object;

    .line 38
    .line 39
    move-object v2, p5

    .line 40
    iput-object v2, v0, Li2/h;->i:Ljava/lang/Class;

    .line 41
    .line 42
    move-object v2, p6

    .line 43
    iput-object v2, v0, Li2/h;->j:Li2/a;

    .line 44
    .line 45
    move v2, p7

    .line 46
    iput v2, v0, Li2/h;->k:I

    .line 47
    .line 48
    move v2, p8

    .line 49
    iput v2, v0, Li2/h;->l:I

    .line 50
    .line 51
    move-object v2, p9

    .line 52
    iput-object v2, v0, Li2/h;->m:Lcom/bumptech/glide/g;

    .line 53
    .line 54
    move-object v2, p10

    .line 55
    iput-object v2, v0, Li2/h;->n:Lj2/i;

    .line 56
    .line 57
    move-object v2, p11

    .line 58
    iput-object v2, v0, Li2/h;->d:Li2/e;

    .line 59
    .line 60
    move-object v2, p12

    .line 61
    iput-object v2, v0, Li2/h;->o:Ljava/util/List;

    .line 62
    .line 63
    move-object/from16 v2, p13

    .line 64
    .line 65
    iput-object v2, v0, Li2/h;->e:Li2/d;

    .line 66
    .line 67
    move-object/from16 v2, p14

    .line 68
    .line 69
    iput-object v2, v0, Li2/h;->u:Lcom/bumptech/glide/load/engine/j;

    .line 70
    .line 71
    move-object/from16 v2, p15

    .line 72
    .line 73
    iput-object v2, v0, Li2/h;->p:Lk2/e;

    .line 74
    .line 75
    move-object/from16 v2, p16

    .line 76
    .line 77
    iput-object v2, v0, Li2/h;->q:Ljava/util/concurrent/Executor;

    .line 78
    .line 79
    sget-object v2, Li2/h$a;->a:Li2/h$a;

    .line 80
    .line 81
    iput-object v2, v0, Li2/h;->v:Li2/h$a;

    .line 82
    .line 83
    iget-object v2, v0, Li2/h;->C:Ljava/lang/RuntimeException;

    .line 84
    .line 85
    if-nez v2, :cond_1

    .line 86
    .line 87
    invoke-virtual {p2}, Lcom/bumptech/glide/e;->i()Z

    .line 88
    .line 89
    .line 90
    move-result v1

    .line 91
    if-eqz v1, :cond_1

    .line 92
    .line 93
    new-instance v1, Ljava/lang/RuntimeException;

    .line 94
    .line 95
    const-string v2, "Glide request origin trace"

    .line 96
    .line 97
    invoke-direct {v1, v2}, Ljava/lang/RuntimeException;-><init>(Ljava/lang/String;)V

    .line 98
    .line 99
    .line 100
    iput-object v1, v0, Li2/h;->C:Ljava/lang/RuntimeException;

    .line 101
    .line 102
    :cond_1
    return-void
.end method

.method private A()V
    .locals 2

    .line 1
    invoke-direct {p0}, Li2/h;->l()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-nez v0, :cond_0

    .line 6
    .line 7
    return-void

    .line 8
    :cond_0
    iget-object v0, p0, Li2/h;->h:Ljava/lang/Object;

    .line 9
    .line 10
    if-nez v0, :cond_1

    .line 11
    .line 12
    invoke-direct {p0}, Li2/h;->p()Landroid/graphics/drawable/Drawable;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    goto :goto_0

    .line 17
    :cond_1
    const/4 v0, 0x0

    .line 18
    :goto_0
    if-nez v0, :cond_2

    .line 19
    .line 20
    invoke-direct {p0}, Li2/h;->o()Landroid/graphics/drawable/Drawable;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    :cond_2
    if-nez v0, :cond_3

    .line 25
    .line 26
    invoke-direct {p0}, Li2/h;->q()Landroid/graphics/drawable/Drawable;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    :cond_3
    iget-object v1, p0, Li2/h;->n:Lj2/i;

    .line 31
    .line 32
    invoke-interface {v1, v0}, Lj2/i;->e(Landroid/graphics/drawable/Drawable;)V

    .line 33
    .line 34
    .line 35
    return-void
.end method

.method private i()V
    .locals 2

    .line 1
    iget-boolean v0, p0, Li2/h;->B:Z

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    return-void

    .line 6
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 7
    .line 8
    const-string v1, "You can\'t start or clear loads in RequestListener or Target callbacks. If you\'re trying to start a fallback request when a load fails, use RequestBuilder#error(RequestBuilder). Otherwise consider posting your into() or clear() calls to the main thread using a Handler instead."

    .line 9
    .line 10
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 11
    .line 12
    .line 13
    throw v0
.end method

.method private k()Z
    .locals 1

    .line 1
    iget-object v0, p0, Li2/h;->e:Li2/d;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    invoke-interface {v0, p0}, Li2/d;->g(Li2/c;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    goto :goto_1

    .line 14
    :cond_1
    :goto_0
    const/4 v0, 0x1

    .line 15
    :goto_1
    return v0
.end method

.method private l()Z
    .locals 1

    .line 1
    iget-object v0, p0, Li2/h;->e:Li2/d;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    invoke-interface {v0, p0}, Li2/d;->e(Li2/c;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    goto :goto_1

    .line 14
    :cond_1
    :goto_0
    const/4 v0, 0x1

    .line 15
    :goto_1
    return v0
.end method

.method private m()Z
    .locals 1

    .line 1
    iget-object v0, p0, Li2/h;->e:Li2/d;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    invoke-interface {v0, p0}, Li2/d;->i(Li2/c;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    goto :goto_1

    .line 14
    :cond_1
    :goto_0
    const/4 v0, 0x1

    .line 15
    :goto_1
    return v0
.end method

.method private n()V
    .locals 1

    .line 1
    invoke-direct {p0}, Li2/h;->i()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Li2/h;->b:Ln2/c;

    .line 5
    .line 6
    invoke-virtual {v0}, Ln2/c;->c()V

    .line 7
    .line 8
    .line 9
    iget-object v0, p0, Li2/h;->n:Lj2/i;

    .line 10
    .line 11
    invoke-interface {v0, p0}, Lj2/i;->f(Lj2/h;)V

    .line 12
    .line 13
    .line 14
    iget-object v0, p0, Li2/h;->s:Lcom/bumptech/glide/load/engine/j$d;

    .line 15
    .line 16
    if-eqz v0, :cond_0

    .line 17
    .line 18
    invoke-virtual {v0}, Lcom/bumptech/glide/load/engine/j$d;->a()V

    .line 19
    .line 20
    .line 21
    const/4 v0, 0x0

    .line 22
    iput-object v0, p0, Li2/h;->s:Lcom/bumptech/glide/load/engine/j$d;

    .line 23
    .line 24
    :cond_0
    return-void
.end method

.method private o()Landroid/graphics/drawable/Drawable;
    .locals 1

    .line 1
    iget-object v0, p0, Li2/h;->w:Landroid/graphics/drawable/Drawable;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Li2/h;->j:Li2/a;

    .line 6
    .line 7
    invoke-virtual {v0}, Li2/a;->l()Landroid/graphics/drawable/Drawable;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    iput-object v0, p0, Li2/h;->w:Landroid/graphics/drawable/Drawable;

    .line 12
    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    iget-object v0, p0, Li2/h;->j:Li2/a;

    .line 16
    .line 17
    invoke-virtual {v0}, Li2/a;->k()I

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-lez v0, :cond_0

    .line 22
    .line 23
    iget-object v0, p0, Li2/h;->j:Li2/a;

    .line 24
    .line 25
    invoke-virtual {v0}, Li2/a;->k()I

    .line 26
    .line 27
    .line 28
    move-result v0

    .line 29
    invoke-direct {p0, v0}, Li2/h;->s(I)Landroid/graphics/drawable/Drawable;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iput-object v0, p0, Li2/h;->w:Landroid/graphics/drawable/Drawable;

    .line 34
    .line 35
    :cond_0
    iget-object v0, p0, Li2/h;->w:Landroid/graphics/drawable/Drawable;

    .line 36
    .line 37
    return-object v0
.end method

.method private p()Landroid/graphics/drawable/Drawable;
    .locals 1

    .line 1
    iget-object v0, p0, Li2/h;->y:Landroid/graphics/drawable/Drawable;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Li2/h;->j:Li2/a;

    .line 6
    .line 7
    invoke-virtual {v0}, Li2/a;->m()Landroid/graphics/drawable/Drawable;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    iput-object v0, p0, Li2/h;->y:Landroid/graphics/drawable/Drawable;

    .line 12
    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    iget-object v0, p0, Li2/h;->j:Li2/a;

    .line 16
    .line 17
    invoke-virtual {v0}, Li2/a;->n()I

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-lez v0, :cond_0

    .line 22
    .line 23
    iget-object v0, p0, Li2/h;->j:Li2/a;

    .line 24
    .line 25
    invoke-virtual {v0}, Li2/a;->n()I

    .line 26
    .line 27
    .line 28
    move-result v0

    .line 29
    invoke-direct {p0, v0}, Li2/h;->s(I)Landroid/graphics/drawable/Drawable;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iput-object v0, p0, Li2/h;->y:Landroid/graphics/drawable/Drawable;

    .line 34
    .line 35
    :cond_0
    iget-object v0, p0, Li2/h;->y:Landroid/graphics/drawable/Drawable;

    .line 36
    .line 37
    return-object v0
.end method

.method private q()Landroid/graphics/drawable/Drawable;
    .locals 1

    .line 1
    iget-object v0, p0, Li2/h;->x:Landroid/graphics/drawable/Drawable;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    iget-object v0, p0, Li2/h;->j:Li2/a;

    .line 6
    .line 7
    invoke-virtual {v0}, Li2/a;->s()Landroid/graphics/drawable/Drawable;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    iput-object v0, p0, Li2/h;->x:Landroid/graphics/drawable/Drawable;

    .line 12
    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    iget-object v0, p0, Li2/h;->j:Li2/a;

    .line 16
    .line 17
    invoke-virtual {v0}, Li2/a;->t()I

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-lez v0, :cond_0

    .line 22
    .line 23
    iget-object v0, p0, Li2/h;->j:Li2/a;

    .line 24
    .line 25
    invoke-virtual {v0}, Li2/a;->t()I

    .line 26
    .line 27
    .line 28
    move-result v0

    .line 29
    invoke-direct {p0, v0}, Li2/h;->s(I)Landroid/graphics/drawable/Drawable;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    iput-object v0, p0, Li2/h;->x:Landroid/graphics/drawable/Drawable;

    .line 34
    .line 35
    :cond_0
    iget-object v0, p0, Li2/h;->x:Landroid/graphics/drawable/Drawable;

    .line 36
    .line 37
    return-object v0
.end method

.method private r()Z
    .locals 1

    .line 1
    iget-object v0, p0, Li2/h;->e:Li2/d;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    invoke-interface {v0}, Li2/d;->getRoot()Li2/d;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    invoke-interface {v0}, Li2/d;->c()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    goto :goto_0

    .line 16
    :cond_0
    const/4 v0, 0x0

    .line 17
    goto :goto_1

    .line 18
    :cond_1
    :goto_0
    const/4 v0, 0x1

    .line 19
    :goto_1
    return v0
.end method

.method private s(I)Landroid/graphics/drawable/Drawable;
    .locals 2

    .line 1
    iget-object v0, p0, Li2/h;->j:Li2/a;

    .line 2
    .line 3
    invoke-virtual {v0}, Li2/a;->y()Landroid/content/res/Resources$Theme;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v0, p0, Li2/h;->j:Li2/a;

    .line 10
    .line 11
    invoke-virtual {v0}, Li2/a;->y()Landroid/content/res/Resources$Theme;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    goto :goto_0

    .line 16
    :cond_0
    iget-object v0, p0, Li2/h;->f:Landroid/content/Context;

    .line 17
    .line 18
    invoke-virtual {v0}, Landroid/content/Context;->getTheme()Landroid/content/res/Resources$Theme;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    :goto_0
    iget-object v1, p0, Li2/h;->g:Lcom/bumptech/glide/e;

    .line 23
    .line 24
    invoke-static {v1, p1, v0}, Lb2/a;->a(Landroid/content/Context;ILandroid/content/res/Resources$Theme;)Landroid/graphics/drawable/Drawable;

    .line 25
    .line 26
    .line 27
    move-result-object p1

    .line 28
    return-object p1
.end method

.method private t(Ljava/lang/String;)V
    .locals 1

    .line 1
    new-instance v0, Ljava/lang/StringBuilder;

    .line 2
    .line 3
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 4
    .line 5
    .line 6
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 7
    .line 8
    .line 9
    const-string p1, " this: "

    .line 10
    .line 11
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 12
    .line 13
    .line 14
    iget-object p1, p0, Li2/h;->a:Ljava/lang/String;

    .line 15
    .line 16
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 17
    .line 18
    .line 19
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object p1

    .line 23
    const-string v0, "Request"

    .line 24
    .line 25
    invoke-static {v0, p1}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    .line 26
    .line 27
    .line 28
    return-void
.end method

.method private static u(IF)I
    .locals 1

    .line 1
    const/high16 v0, -0x80000000

    .line 2
    .line 3
    if-ne p0, v0, :cond_0

    .line 4
    .line 5
    goto :goto_0

    .line 6
    :cond_0
    int-to-float p0, p0

    .line 7
    mul-float p1, p1, p0

    .line 8
    .line 9
    invoke-static {p1}, Ljava/lang/Math;->round(F)I

    .line 10
    .line 11
    .line 12
    move-result p0

    .line 13
    :goto_0
    return p0
.end method

.method private v()V
    .locals 1

    .line 1
    iget-object v0, p0, Li2/h;->e:Li2/d;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0, p0}, Li2/d;->a(Li2/c;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method private w()V
    .locals 1

    .line 1
    iget-object v0, p0, Li2/h;->e:Li2/d;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    invoke-interface {v0, p0}, Li2/d;->b(Li2/c;)V

    .line 6
    .line 7
    .line 8
    :cond_0
    return-void
.end method

.method public static x(Landroid/content/Context;Lcom/bumptech/glide/e;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Class;Li2/a;IILcom/bumptech/glide/g;Lj2/i;Li2/e;Ljava/util/List;Li2/d;Lcom/bumptech/glide/load/engine/j;Lk2/e;Ljava/util/concurrent/Executor;)Li2/h;
    .locals 18
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "<R:",
            "Ljava/lang/Object;",
            ">(",
            "Landroid/content/Context;",
            "Lcom/bumptech/glide/e;",
            "Ljava/lang/Object;",
            "Ljava/lang/Object;",
            "Ljava/lang/Class<",
            "TR;>;",
            "Li2/a<",
            "*>;II",
            "Lcom/bumptech/glide/g;",
            "Lj2/i<",
            "TR;>;",
            "Li2/e<",
            "TR;>;",
            "Ljava/util/List<",
            "Li2/e<",
            "TR;>;>;",
            "Li2/d;",
            "Lcom/bumptech/glide/load/engine/j;",
            "Lk2/e<",
            "-TR;>;",
            "Ljava/util/concurrent/Executor;",
            ")",
            "Li2/h<",
            "TR;>;"
        }
    .end annotation

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    move-object/from16 v2, p1

    .line 4
    .line 5
    move-object/from16 v3, p2

    .line 6
    .line 7
    move-object/from16 v4, p3

    .line 8
    .line 9
    move-object/from16 v5, p4

    .line 10
    .line 11
    move-object/from16 v6, p5

    .line 12
    .line 13
    move/from16 v7, p6

    .line 14
    .line 15
    move/from16 v8, p7

    .line 16
    .line 17
    move-object/from16 v9, p8

    .line 18
    .line 19
    move-object/from16 v10, p9

    .line 20
    .line 21
    move-object/from16 v11, p10

    .line 22
    .line 23
    move-object/from16 v12, p11

    .line 24
    .line 25
    move-object/from16 v13, p12

    .line 26
    .line 27
    move-object/from16 v14, p13

    .line 28
    .line 29
    move-object/from16 v15, p14

    .line 30
    .line 31
    move-object/from16 v16, p15

    .line 32
    .line 33
    new-instance v17, Li2/h;

    .line 34
    .line 35
    move-object/from16 v0, v17

    .line 36
    .line 37
    invoke-direct/range {v0 .. v16}, Li2/h;-><init>(Landroid/content/Context;Lcom/bumptech/glide/e;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Class;Li2/a;IILcom/bumptech/glide/g;Lj2/i;Li2/e;Ljava/util/List;Li2/d;Lcom/bumptech/glide/load/engine/j;Lk2/e;Ljava/util/concurrent/Executor;)V

    .line 38
    .line 39
    .line 40
    return-object v17
.end method

.method private y(Lcom/bumptech/glide/load/engine/GlideException;I)V
    .locals 8

    .line 1
    iget-object v0, p0, Li2/h;->b:Ln2/c;

    .line 2
    .line 3
    invoke-virtual {v0}, Ln2/c;->c()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Li2/h;->c:Ljava/lang/Object;

    .line 7
    .line 8
    monitor-enter v0

    .line 9
    :try_start_0
    iget-object v1, p0, Li2/h;->C:Ljava/lang/RuntimeException;

    .line 10
    .line 11
    invoke-virtual {p1, v1}, Lcom/bumptech/glide/load/engine/GlideException;->k(Ljava/lang/Exception;)V

    .line 12
    .line 13
    .line 14
    iget-object v1, p0, Li2/h;->g:Lcom/bumptech/glide/e;

    .line 15
    .line 16
    invoke-virtual {v1}, Lcom/bumptech/glide/e;->g()I

    .line 17
    .line 18
    .line 19
    move-result v1

    .line 20
    if-gt v1, p2, :cond_0

    .line 21
    .line 22
    const-string p2, "Glide"

    .line 23
    .line 24
    new-instance v2, Ljava/lang/StringBuilder;

    .line 25
    .line 26
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 27
    .line 28
    .line 29
    const-string v3, "Load failed for "

    .line 30
    .line 31
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 32
    .line 33
    .line 34
    iget-object v3, p0, Li2/h;->h:Ljava/lang/Object;

    .line 35
    .line 36
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 37
    .line 38
    .line 39
    const-string v3, " with size ["

    .line 40
    .line 41
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 42
    .line 43
    .line 44
    iget v3, p0, Li2/h;->z:I

    .line 45
    .line 46
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 47
    .line 48
    .line 49
    const-string v3, "x"

    .line 50
    .line 51
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 52
    .line 53
    .line 54
    iget v3, p0, Li2/h;->A:I

    .line 55
    .line 56
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 57
    .line 58
    .line 59
    const-string v3, "]"

    .line 60
    .line 61
    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 62
    .line 63
    .line 64
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 65
    .line 66
    .line 67
    move-result-object v2

    .line 68
    invoke-static {p2, v2, p1}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)I

    .line 69
    .line 70
    .line 71
    const/4 p2, 0x4

    .line 72
    if-gt v1, p2, :cond_0

    .line 73
    .line 74
    const-string p2, "Glide"

    .line 75
    .line 76
    invoke-virtual {p1, p2}, Lcom/bumptech/glide/load/engine/GlideException;->g(Ljava/lang/String;)V

    .line 77
    .line 78
    .line 79
    :cond_0
    const/4 p2, 0x0

    .line 80
    iput-object p2, p0, Li2/h;->s:Lcom/bumptech/glide/load/engine/j$d;

    .line 81
    .line 82
    sget-object p2, Li2/h$a;->e:Li2/h$a;

    .line 83
    .line 84
    iput-object p2, p0, Li2/h;->v:Li2/h$a;

    .line 85
    .line 86
    const/4 p2, 0x1

    .line 87
    iput-boolean p2, p0, Li2/h;->B:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    .line 88
    .line 89
    const/4 v1, 0x0

    .line 90
    :try_start_1
    iget-object v2, p0, Li2/h;->o:Ljava/util/List;

    .line 91
    .line 92
    if-eqz v2, :cond_1

    .line 93
    .line 94
    invoke-interface {v2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 95
    .line 96
    .line 97
    move-result-object v2

    .line 98
    const/4 v3, 0x0

    .line 99
    :goto_0
    invoke-interface {v2}, Ljava/util/Iterator;->hasNext()Z

    .line 100
    .line 101
    .line 102
    move-result v4

    .line 103
    if-eqz v4, :cond_2

    .line 104
    .line 105
    invoke-interface {v2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 106
    .line 107
    .line 108
    move-result-object v4

    .line 109
    check-cast v4, Li2/e;

    .line 110
    .line 111
    iget-object v5, p0, Li2/h;->h:Ljava/lang/Object;

    .line 112
    .line 113
    iget-object v6, p0, Li2/h;->n:Lj2/i;

    .line 114
    .line 115
    invoke-direct {p0}, Li2/h;->r()Z

    .line 116
    .line 117
    .line 118
    move-result v7

    .line 119
    invoke-interface {v4, p1, v5, v6, v7}, Li2/e;->b(Lcom/bumptech/glide/load/engine/GlideException;Ljava/lang/Object;Lj2/i;Z)Z

    .line 120
    .line 121
    .line 122
    move-result v4

    .line 123
    or-int/2addr v3, v4

    .line 124
    goto :goto_0

    .line 125
    :cond_1
    const/4 v3, 0x0

    .line 126
    :cond_2
    iget-object v2, p0, Li2/h;->d:Li2/e;

    .line 127
    .line 128
    if-eqz v2, :cond_3

    .line 129
    .line 130
    iget-object v4, p0, Li2/h;->h:Ljava/lang/Object;

    .line 131
    .line 132
    iget-object v5, p0, Li2/h;->n:Lj2/i;

    .line 133
    .line 134
    invoke-direct {p0}, Li2/h;->r()Z

    .line 135
    .line 136
    .line 137
    move-result v6

    .line 138
    invoke-interface {v2, p1, v4, v5, v6}, Li2/e;->b(Lcom/bumptech/glide/load/engine/GlideException;Ljava/lang/Object;Lj2/i;Z)Z

    .line 139
    .line 140
    .line 141
    move-result p1

    .line 142
    if-eqz p1, :cond_3

    .line 143
    .line 144
    goto :goto_1

    .line 145
    :cond_3
    const/4 p2, 0x0

    .line 146
    :goto_1
    or-int p1, v3, p2

    .line 147
    .line 148
    if-nez p1, :cond_4

    .line 149
    .line 150
    invoke-direct {p0}, Li2/h;->A()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 151
    .line 152
    .line 153
    :cond_4
    :try_start_2
    iput-boolean v1, p0, Li2/h;->B:Z

    .line 154
    .line 155
    invoke-direct {p0}, Li2/h;->v()V

    .line 156
    .line 157
    .line 158
    monitor-exit v0

    .line 159
    return-void

    .line 160
    :catchall_0
    move-exception p1

    .line 161
    iput-boolean v1, p0, Li2/h;->B:Z

    .line 162
    .line 163
    throw p1

    .line 164
    :catchall_1
    move-exception p1

    .line 165
    monitor-exit v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 166
    throw p1
.end method

.method private z(Lt1/c;Ljava/lang/Object;Lq1/a;)V
    .locals 10
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lt1/c<",
            "TR;>;TR;",
            "Lq1/a;",
            ")V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Li2/h;->r()Z

    .line 2
    .line 3
    .line 4
    move-result v6

    .line 5
    sget-object v0, Li2/h$a;->d:Li2/h$a;

    .line 6
    .line 7
    iput-object v0, p0, Li2/h;->v:Li2/h$a;

    .line 8
    .line 9
    iput-object p1, p0, Li2/h;->r:Lt1/c;

    .line 10
    .line 11
    iget-object p1, p0, Li2/h;->g:Lcom/bumptech/glide/e;

    .line 12
    .line 13
    invoke-virtual {p1}, Lcom/bumptech/glide/e;->g()I

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    const/4 v0, 0x3

    .line 18
    if-gt p1, v0, :cond_0

    .line 19
    .line 20
    new-instance p1, Ljava/lang/StringBuilder;

    .line 21
    .line 22
    invoke-direct {p1}, Ljava/lang/StringBuilder;-><init>()V

    .line 23
    .line 24
    .line 25
    const-string v0, "Finished loading "

    .line 26
    .line 27
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 28
    .line 29
    .line 30
    invoke-virtual {p2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 31
    .line 32
    .line 33
    move-result-object v0

    .line 34
    invoke-virtual {v0}, Ljava/lang/Class;->getSimpleName()Ljava/lang/String;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 39
    .line 40
    .line 41
    const-string v0, " from "

    .line 42
    .line 43
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 44
    .line 45
    .line 46
    invoke-virtual {p1, p3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 47
    .line 48
    .line 49
    const-string v0, " for "

    .line 50
    .line 51
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 52
    .line 53
    .line 54
    iget-object v0, p0, Li2/h;->h:Ljava/lang/Object;

    .line 55
    .line 56
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 57
    .line 58
    .line 59
    const-string v0, " with size ["

    .line 60
    .line 61
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 62
    .line 63
    .line 64
    iget v0, p0, Li2/h;->z:I

    .line 65
    .line 66
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 67
    .line 68
    .line 69
    const-string v0, "x"

    .line 70
    .line 71
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 72
    .line 73
    .line 74
    iget v0, p0, Li2/h;->A:I

    .line 75
    .line 76
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    .line 77
    .line 78
    .line 79
    const-string v0, "] in "

    .line 80
    .line 81
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 82
    .line 83
    .line 84
    iget-wide v0, p0, Li2/h;->t:J

    .line 85
    .line 86
    invoke-static {v0, v1}, Lm2/f;->a(J)D

    .line 87
    .line 88
    .line 89
    move-result-wide v0

    .line 90
    invoke-virtual {p1, v0, v1}, Ljava/lang/StringBuilder;->append(D)Ljava/lang/StringBuilder;

    .line 91
    .line 92
    .line 93
    const-string v0, " ms"

    .line 94
    .line 95
    invoke-virtual {p1, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 96
    .line 97
    .line 98
    invoke-virtual {p1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 99
    .line 100
    .line 101
    move-result-object p1

    .line 102
    const-string v0, "Glide"

    .line 103
    .line 104
    invoke-static {v0, p1}, Landroid/util/Log;->d(Ljava/lang/String;Ljava/lang/String;)I

    .line 105
    .line 106
    .line 107
    :cond_0
    const/4 p1, 0x1

    .line 108
    iput-boolean p1, p0, Li2/h;->B:Z

    .line 109
    .line 110
    const/4 v7, 0x0

    .line 111
    :try_start_0
    iget-object v0, p0, Li2/h;->o:Ljava/util/List;

    .line 112
    .line 113
    if-eqz v0, :cond_1

    .line 114
    .line 115
    invoke-interface {v0}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 116
    .line 117
    .line 118
    move-result-object v8

    .line 119
    const/4 v9, 0x0

    .line 120
    :goto_0
    invoke-interface {v8}, Ljava/util/Iterator;->hasNext()Z

    .line 121
    .line 122
    .line 123
    move-result v0

    .line 124
    if-eqz v0, :cond_2

    .line 125
    .line 126
    invoke-interface {v8}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 127
    .line 128
    .line 129
    move-result-object v0

    .line 130
    check-cast v0, Li2/e;

    .line 131
    .line 132
    iget-object v2, p0, Li2/h;->h:Ljava/lang/Object;

    .line 133
    .line 134
    iget-object v3, p0, Li2/h;->n:Lj2/i;

    .line 135
    .line 136
    move-object v1, p2

    .line 137
    move-object v4, p3

    .line 138
    move v5, v6

    .line 139
    invoke-interface/range {v0 .. v5}, Li2/e;->a(Ljava/lang/Object;Ljava/lang/Object;Lj2/i;Lq1/a;Z)Z

    .line 140
    .line 141
    .line 142
    move-result v0

    .line 143
    or-int/2addr v9, v0

    .line 144
    goto :goto_0

    .line 145
    :cond_1
    const/4 v9, 0x0

    .line 146
    :cond_2
    iget-object v0, p0, Li2/h;->d:Li2/e;

    .line 147
    .line 148
    if-eqz v0, :cond_3

    .line 149
    .line 150
    iget-object v2, p0, Li2/h;->h:Ljava/lang/Object;

    .line 151
    .line 152
    iget-object v3, p0, Li2/h;->n:Lj2/i;

    .line 153
    .line 154
    move-object v1, p2

    .line 155
    move-object v4, p3

    .line 156
    move v5, v6

    .line 157
    invoke-interface/range {v0 .. v5}, Li2/e;->a(Ljava/lang/Object;Ljava/lang/Object;Lj2/i;Lq1/a;Z)Z

    .line 158
    .line 159
    .line 160
    move-result v0

    .line 161
    if-eqz v0, :cond_3

    .line 162
    .line 163
    goto :goto_1

    .line 164
    :cond_3
    const/4 p1, 0x0

    .line 165
    :goto_1
    or-int/2addr p1, v9

    .line 166
    if-nez p1, :cond_4

    .line 167
    .line 168
    iget-object p1, p0, Li2/h;->p:Lk2/e;

    .line 169
    .line 170
    invoke-interface {p1, p3, v6}, Lk2/e;->a(Lq1/a;Z)Lk2/d;

    .line 171
    .line 172
    .line 173
    move-result-object p1

    .line 174
    iget-object p3, p0, Li2/h;->n:Lj2/i;

    .line 175
    .line 176
    invoke-interface {p3, p2, p1}, Lj2/i;->b(Ljava/lang/Object;Lk2/d;)V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 177
    .line 178
    .line 179
    :cond_4
    iput-boolean v7, p0, Li2/h;->B:Z

    .line 180
    .line 181
    invoke-direct {p0}, Li2/h;->w()V

    .line 182
    .line 183
    .line 184
    return-void

    .line 185
    :catchall_0
    move-exception p1

    .line 186
    iput-boolean v7, p0, Li2/h;->B:Z

    .line 187
    .line 188
    throw p1
.end method


# virtual methods
.method public a(Lt1/c;Lq1/a;)V
    .locals 5
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lt1/c<",
            "*>;",
            "Lq1/a;",
            ")V"
        }
    .end annotation

    .line 1
    iget-object v0, p0, Li2/h;->b:Ln2/c;

    .line 2
    .line 3
    invoke-virtual {v0}, Ln2/c;->c()V

    .line 4
    .line 5
    .line 6
    const/4 v0, 0x0

    .line 7
    :try_start_0
    iget-object v1, p0, Li2/h;->c:Ljava/lang/Object;

    .line 8
    .line 9
    monitor-enter v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_2

    .line 10
    :try_start_1
    iput-object v0, p0, Li2/h;->s:Lcom/bumptech/glide/load/engine/j$d;

    .line 11
    .line 12
    if-nez p1, :cond_0

    .line 13
    .line 14
    new-instance p1, Lcom/bumptech/glide/load/engine/GlideException;

    .line 15
    .line 16
    new-instance p2, Ljava/lang/StringBuilder;

    .line 17
    .line 18
    invoke-direct {p2}, Ljava/lang/StringBuilder;-><init>()V

    .line 19
    .line 20
    .line 21
    const-string v2, "Expected to receive a Resource<R> with an object of "

    .line 22
    .line 23
    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 24
    .line 25
    .line 26
    iget-object v2, p0, Li2/h;->i:Ljava/lang/Class;

    .line 27
    .line 28
    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 29
    .line 30
    .line 31
    const-string v2, " inside, but instead got null."

    .line 32
    .line 33
    invoke-virtual {p2, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 34
    .line 35
    .line 36
    invoke-virtual {p2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 37
    .line 38
    .line 39
    move-result-object p2

    .line 40
    invoke-direct {p1, p2}, Lcom/bumptech/glide/load/engine/GlideException;-><init>(Ljava/lang/String;)V

    .line 41
    .line 42
    .line 43
    invoke-virtual {p0, p1}, Li2/h;->b(Lcom/bumptech/glide/load/engine/GlideException;)V

    .line 44
    .line 45
    .line 46
    monitor-exit v1

    .line 47
    return-void

    .line 48
    :cond_0
    invoke-interface {p1}, Lt1/c;->get()Ljava/lang/Object;

    .line 49
    .line 50
    .line 51
    move-result-object v2

    .line 52
    if-eqz v2, :cond_3

    .line 53
    .line 54
    iget-object v3, p0, Li2/h;->i:Ljava/lang/Class;

    .line 55
    .line 56
    invoke-virtual {v2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 57
    .line 58
    .line 59
    move-result-object v4

    .line 60
    invoke-virtual {v3, v4}, Ljava/lang/Class;->isAssignableFrom(Ljava/lang/Class;)Z

    .line 61
    .line 62
    .line 63
    move-result v3

    .line 64
    if-nez v3, :cond_1

    .line 65
    .line 66
    goto :goto_0

    .line 67
    :cond_1
    invoke-direct {p0}, Li2/h;->m()Z

    .line 68
    .line 69
    .line 70
    move-result v3
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    .line 71
    if-nez v3, :cond_2

    .line 72
    .line 73
    :try_start_2
    iput-object v0, p0, Li2/h;->r:Lt1/c;

    .line 74
    .line 75
    sget-object p2, Li2/h$a;->d:Li2/h$a;

    .line 76
    .line 77
    iput-object p2, p0, Li2/h;->v:Li2/h$a;

    .line 78
    .line 79
    monitor-exit v1
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 80
    iget-object p2, p0, Li2/h;->u:Lcom/bumptech/glide/load/engine/j;

    .line 81
    .line 82
    invoke-virtual {p2, p1}, Lcom/bumptech/glide/load/engine/j;->k(Lt1/c;)V

    .line 83
    .line 84
    .line 85
    return-void

    .line 86
    :cond_2
    :try_start_3
    invoke-direct {p0, p1, v2, p2}, Li2/h;->z(Lt1/c;Ljava/lang/Object;Lq1/a;)V

    .line 87
    .line 88
    .line 89
    monitor-exit v1
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    .line 90
    return-void

    .line 91
    :cond_3
    :goto_0
    :try_start_4
    iput-object v0, p0, Li2/h;->r:Lt1/c;

    .line 92
    .line 93
    new-instance p2, Lcom/bumptech/glide/load/engine/GlideException;

    .line 94
    .line 95
    new-instance v0, Ljava/lang/StringBuilder;

    .line 96
    .line 97
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 98
    .line 99
    .line 100
    const-string v3, "Expected to receive an object of "

    .line 101
    .line 102
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 103
    .line 104
    .line 105
    iget-object v3, p0, Li2/h;->i:Ljava/lang/Class;

    .line 106
    .line 107
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 108
    .line 109
    .line 110
    const-string v3, " but instead got "

    .line 111
    .line 112
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 113
    .line 114
    .line 115
    if-eqz v2, :cond_4

    .line 116
    .line 117
    invoke-virtual {v2}, Ljava/lang/Object;->getClass()Ljava/lang/Class;

    .line 118
    .line 119
    .line 120
    move-result-object v3

    .line 121
    goto :goto_1

    .line 122
    :cond_4
    const-string v3, ""

    .line 123
    .line 124
    :goto_1
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 125
    .line 126
    .line 127
    const-string v3, "{"

    .line 128
    .line 129
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 130
    .line 131
    .line 132
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 133
    .line 134
    .line 135
    const-string v3, "} inside Resource{"

    .line 136
    .line 137
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 138
    .line 139
    .line 140
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 141
    .line 142
    .line 143
    const-string v3, "}."

    .line 144
    .line 145
    invoke-virtual {v0, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 146
    .line 147
    .line 148
    if-eqz v2, :cond_5

    .line 149
    .line 150
    const-string v2, ""

    .line 151
    .line 152
    goto :goto_2

    .line 153
    :cond_5
    const-string v2, " To indicate failure return a null Resource object, rather than a Resource object containing null data."

    .line 154
    .line 155
    :goto_2
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 156
    .line 157
    .line 158
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 159
    .line 160
    .line 161
    move-result-object v0

    .line 162
    invoke-direct {p2, v0}, Lcom/bumptech/glide/load/engine/GlideException;-><init>(Ljava/lang/String;)V

    .line 163
    .line 164
    .line 165
    invoke-virtual {p0, p2}, Li2/h;->b(Lcom/bumptech/glide/load/engine/GlideException;)V

    .line 166
    .line 167
    .line 168
    monitor-exit v1
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    .line 169
    iget-object p2, p0, Li2/h;->u:Lcom/bumptech/glide/load/engine/j;

    .line 170
    .line 171
    invoke-virtual {p2, p1}, Lcom/bumptech/glide/load/engine/j;->k(Lt1/c;)V

    .line 172
    .line 173
    .line 174
    return-void

    .line 175
    :catchall_0
    move-exception p2

    .line 176
    move-object v0, p1

    .line 177
    move-object p1, p2

    .line 178
    goto :goto_3

    .line 179
    :catchall_1
    move-exception p1

    .line 180
    :goto_3
    :try_start_5
    monitor-exit v1
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_1

    .line 181
    :try_start_6
    throw p1
    :try_end_6
    .catchall {:try_start_6 .. :try_end_6} :catchall_2

    .line 182
    :catchall_2
    move-exception p1

    .line 183
    if-eqz v0, :cond_6

    .line 184
    .line 185
    iget-object p2, p0, Li2/h;->u:Lcom/bumptech/glide/load/engine/j;

    .line 186
    .line 187
    invoke-virtual {p2, v0}, Lcom/bumptech/glide/load/engine/j;->k(Lt1/c;)V

    .line 188
    .line 189
    .line 190
    :cond_6
    throw p1
.end method

.method public b(Lcom/bumptech/glide/load/engine/GlideException;)V
    .locals 1

    .line 1
    const/4 v0, 0x5

    .line 2
    invoke-direct {p0, p1, v0}, Li2/h;->y(Lcom/bumptech/glide/load/engine/GlideException;I)V

    .line 3
    .line 4
    .line 5
    return-void
.end method

.method public c()Z
    .locals 3

    .line 1
    iget-object v0, p0, Li2/h;->c:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    iget-object v1, p0, Li2/h;->v:Li2/h$a;

    .line 5
    .line 6
    sget-object v2, Li2/h$a;->d:Li2/h$a;

    .line 7
    .line 8
    if-ne v1, v2, :cond_0

    .line 9
    .line 10
    const/4 v1, 0x1

    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v1, 0x0

    .line 13
    :goto_0
    monitor-exit v0

    .line 14
    return v1

    .line 15
    :catchall_0
    move-exception v1

    .line 16
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 17
    throw v1
.end method

.method public clear()V
    .locals 5

    .line 1
    iget-object v0, p0, Li2/h;->c:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    invoke-direct {p0}, Li2/h;->i()V

    .line 5
    .line 6
    .line 7
    iget-object v1, p0, Li2/h;->b:Ln2/c;

    .line 8
    .line 9
    invoke-virtual {v1}, Ln2/c;->c()V

    .line 10
    .line 11
    .line 12
    iget-object v1, p0, Li2/h;->v:Li2/h$a;

    .line 13
    .line 14
    sget-object v2, Li2/h$a;->f:Li2/h$a;

    .line 15
    .line 16
    if-ne v1, v2, :cond_0

    .line 17
    .line 18
    monitor-exit v0

    .line 19
    return-void

    .line 20
    :cond_0
    invoke-direct {p0}, Li2/h;->n()V

    .line 21
    .line 22
    .line 23
    iget-object v1, p0, Li2/h;->r:Lt1/c;

    .line 24
    .line 25
    const/4 v3, 0x0

    .line 26
    if-eqz v1, :cond_1

    .line 27
    .line 28
    iput-object v3, p0, Li2/h;->r:Lt1/c;

    .line 29
    .line 30
    goto :goto_0

    .line 31
    :cond_1
    move-object v1, v3

    .line 32
    :goto_0
    invoke-direct {p0}, Li2/h;->k()Z

    .line 33
    .line 34
    .line 35
    move-result v3

    .line 36
    if-eqz v3, :cond_2

    .line 37
    .line 38
    iget-object v3, p0, Li2/h;->n:Lj2/i;

    .line 39
    .line 40
    invoke-direct {p0}, Li2/h;->q()Landroid/graphics/drawable/Drawable;

    .line 41
    .line 42
    .line 43
    move-result-object v4

    .line 44
    invoke-interface {v3, v4}, Lj2/i;->j(Landroid/graphics/drawable/Drawable;)V

    .line 45
    .line 46
    .line 47
    :cond_2
    iput-object v2, p0, Li2/h;->v:Li2/h$a;

    .line 48
    .line 49
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 50
    if-eqz v1, :cond_3

    .line 51
    .line 52
    iget-object v0, p0, Li2/h;->u:Lcom/bumptech/glide/load/engine/j;

    .line 53
    .line 54
    invoke-virtual {v0, v1}, Lcom/bumptech/glide/load/engine/j;->k(Lt1/c;)V

    .line 55
    .line 56
    .line 57
    :cond_3
    return-void

    .line 58
    :catchall_0
    move-exception v1

    .line 59
    :try_start_1
    monitor-exit v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 60
    throw v1
.end method

.method public d(Li2/c;)Z
    .locals 16

    .line 1
    move-object/from16 v1, p0

    .line 2
    .line 3
    move-object/from16 v0, p1

    .line 4
    .line 5
    instance-of v2, v0, Li2/h;

    .line 6
    .line 7
    const/4 v3, 0x0

    .line 8
    if-nez v2, :cond_0

    .line 9
    .line 10
    return v3

    .line 11
    :cond_0
    iget-object v2, v1, Li2/h;->c:Ljava/lang/Object;

    .line 12
    .line 13
    monitor-enter v2

    .line 14
    :try_start_0
    iget v4, v1, Li2/h;->k:I

    .line 15
    .line 16
    iget v5, v1, Li2/h;->l:I

    .line 17
    .line 18
    iget-object v6, v1, Li2/h;->h:Ljava/lang/Object;

    .line 19
    .line 20
    iget-object v7, v1, Li2/h;->i:Ljava/lang/Class;

    .line 21
    .line 22
    iget-object v8, v1, Li2/h;->j:Li2/a;

    .line 23
    .line 24
    iget-object v9, v1, Li2/h;->m:Lcom/bumptech/glide/g;

    .line 25
    .line 26
    iget-object v10, v1, Li2/h;->o:Ljava/util/List;

    .line 27
    .line 28
    if-eqz v10, :cond_1

    .line 29
    .line 30
    invoke-interface {v10}, Ljava/util/List;->size()I

    .line 31
    .line 32
    .line 33
    move-result v10

    .line 34
    goto :goto_0

    .line 35
    :cond_1
    const/4 v10, 0x0

    .line 36
    :goto_0
    monitor-exit v2
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    .line 37
    check-cast v0, Li2/h;

    .line 38
    .line 39
    iget-object v11, v0, Li2/h;->c:Ljava/lang/Object;

    .line 40
    .line 41
    monitor-enter v11

    .line 42
    :try_start_1
    iget v2, v0, Li2/h;->k:I

    .line 43
    .line 44
    iget v12, v0, Li2/h;->l:I

    .line 45
    .line 46
    iget-object v13, v0, Li2/h;->h:Ljava/lang/Object;

    .line 47
    .line 48
    iget-object v14, v0, Li2/h;->i:Ljava/lang/Class;

    .line 49
    .line 50
    iget-object v15, v0, Li2/h;->j:Li2/a;

    .line 51
    .line 52
    iget-object v3, v0, Li2/h;->m:Lcom/bumptech/glide/g;

    .line 53
    .line 54
    iget-object v0, v0, Li2/h;->o:Ljava/util/List;

    .line 55
    .line 56
    if-eqz v0, :cond_2

    .line 57
    .line 58
    invoke-interface {v0}, Ljava/util/List;->size()I

    .line 59
    .line 60
    .line 61
    move-result v0

    .line 62
    goto :goto_1

    .line 63
    :cond_2
    const/4 v0, 0x0

    .line 64
    :goto_1
    monitor-exit v11
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 65
    if-ne v4, v2, :cond_3

    .line 66
    .line 67
    if-ne v5, v12, :cond_3

    .line 68
    .line 69
    invoke-static {v6, v13}, Lm2/k;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 70
    .line 71
    .line 72
    move-result v2

    .line 73
    if-eqz v2, :cond_3

    .line 74
    .line 75
    invoke-virtual {v7, v14}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    .line 76
    .line 77
    .line 78
    move-result v2

    .line 79
    if-eqz v2, :cond_3

    .line 80
    .line 81
    invoke-virtual {v8, v15}, Li2/a;->equals(Ljava/lang/Object;)Z

    .line 82
    .line 83
    .line 84
    move-result v2

    .line 85
    if-eqz v2, :cond_3

    .line 86
    .line 87
    if-ne v9, v3, :cond_3

    .line 88
    .line 89
    if-ne v10, v0, :cond_3

    .line 90
    .line 91
    const/4 v3, 0x1

    .line 92
    goto :goto_2

    .line 93
    :cond_3
    const/4 v3, 0x0

    .line 94
    :goto_2
    return v3

    .line 95
    :catchall_0
    move-exception v0

    .line 96
    :try_start_2
    monitor-exit v11
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 97
    throw v0

    .line 98
    :catchall_1
    move-exception v0

    .line 99
    :try_start_3
    monitor-exit v2
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_1

    .line 100
    throw v0
.end method

.method public e(II)V
    .locals 24

    .line 1
    move-object/from16 v15, p0

    .line 2
    .line 3
    iget-object v0, v15, Li2/h;->b:Ln2/c;

    .line 4
    .line 5
    invoke-virtual {v0}, Ln2/c;->c()V

    .line 6
    .line 7
    .line 8
    iget-object v14, v15, Li2/h;->c:Ljava/lang/Object;

    .line 9
    .line 10
    monitor-enter v14

    .line 11
    :try_start_0
    sget-boolean v0, Li2/h;->D:Z

    .line 12
    .line 13
    if-eqz v0, :cond_0

    .line 14
    .line 15
    new-instance v1, Ljava/lang/StringBuilder;

    .line 16
    .line 17
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 18
    .line 19
    .line 20
    const-string v2, "Got onSizeReady in "

    .line 21
    .line 22
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 23
    .line 24
    .line 25
    iget-wide v2, v15, Li2/h;->t:J

    .line 26
    .line 27
    invoke-static {v2, v3}, Lm2/f;->a(J)D

    .line 28
    .line 29
    .line 30
    move-result-wide v2

    .line 31
    invoke-virtual {v1, v2, v3}, Ljava/lang/StringBuilder;->append(D)Ljava/lang/StringBuilder;

    .line 32
    .line 33
    .line 34
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    invoke-direct {v15, v1}, Li2/h;->t(Ljava/lang/String;)V

    .line 39
    .line 40
    .line 41
    :cond_0
    iget-object v1, v15, Li2/h;->v:Li2/h$a;

    .line 42
    .line 43
    sget-object v2, Li2/h$a;->c:Li2/h$a;

    .line 44
    .line 45
    if-eq v1, v2, :cond_1

    .line 46
    .line 47
    monitor-exit v14

    .line 48
    return-void

    .line 49
    :cond_1
    sget-object v13, Li2/h$a;->b:Li2/h$a;

    .line 50
    .line 51
    iput-object v13, v15, Li2/h;->v:Li2/h$a;

    .line 52
    .line 53
    iget-object v1, v15, Li2/h;->j:Li2/a;

    .line 54
    .line 55
    invoke-virtual {v1}, Li2/a;->x()F

    .line 56
    .line 57
    .line 58
    move-result v1

    .line 59
    move/from16 v2, p1

    .line 60
    .line 61
    invoke-static {v2, v1}, Li2/h;->u(IF)I

    .line 62
    .line 63
    .line 64
    move-result v2

    .line 65
    iput v2, v15, Li2/h;->z:I

    .line 66
    .line 67
    move/from16 v2, p2

    .line 68
    .line 69
    invoke-static {v2, v1}, Li2/h;->u(IF)I

    .line 70
    .line 71
    .line 72
    move-result v1

    .line 73
    iput v1, v15, Li2/h;->A:I

    .line 74
    .line 75
    if-eqz v0, :cond_2

    .line 76
    .line 77
    new-instance v1, Ljava/lang/StringBuilder;

    .line 78
    .line 79
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 80
    .line 81
    .line 82
    const-string v2, "finished setup for calling load in "

    .line 83
    .line 84
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 85
    .line 86
    .line 87
    iget-wide v2, v15, Li2/h;->t:J

    .line 88
    .line 89
    invoke-static {v2, v3}, Lm2/f;->a(J)D

    .line 90
    .line 91
    .line 92
    move-result-wide v2

    .line 93
    invoke-virtual {v1, v2, v3}, Ljava/lang/StringBuilder;->append(D)Ljava/lang/StringBuilder;

    .line 94
    .line 95
    .line 96
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 97
    .line 98
    .line 99
    move-result-object v1

    .line 100
    invoke-direct {v15, v1}, Li2/h;->t(Ljava/lang/String;)V

    .line 101
    .line 102
    .line 103
    :cond_2
    iget-object v1, v15, Li2/h;->u:Lcom/bumptech/glide/load/engine/j;

    .line 104
    .line 105
    iget-object v2, v15, Li2/h;->g:Lcom/bumptech/glide/e;

    .line 106
    .line 107
    iget-object v3, v15, Li2/h;->h:Ljava/lang/Object;

    .line 108
    .line 109
    iget-object v4, v15, Li2/h;->j:Li2/a;

    .line 110
    .line 111
    invoke-virtual {v4}, Li2/a;->w()Lq1/e;

    .line 112
    .line 113
    .line 114
    move-result-object v4

    .line 115
    iget v5, v15, Li2/h;->z:I

    .line 116
    .line 117
    iget v6, v15, Li2/h;->A:I

    .line 118
    .line 119
    iget-object v7, v15, Li2/h;->j:Li2/a;

    .line 120
    .line 121
    invoke-virtual {v7}, Li2/a;->v()Ljava/lang/Class;

    .line 122
    .line 123
    .line 124
    move-result-object v7

    .line 125
    iget-object v8, v15, Li2/h;->i:Ljava/lang/Class;

    .line 126
    .line 127
    iget-object v9, v15, Li2/h;->m:Lcom/bumptech/glide/g;

    .line 128
    .line 129
    iget-object v10, v15, Li2/h;->j:Li2/a;

    .line 130
    .line 131
    invoke-virtual {v10}, Li2/a;->j()Lt1/a;

    .line 132
    .line 133
    .line 134
    move-result-object v10

    .line 135
    iget-object v11, v15, Li2/h;->j:Li2/a;

    .line 136
    .line 137
    invoke-virtual {v11}, Li2/a;->z()Ljava/util/Map;

    .line 138
    .line 139
    .line 140
    move-result-object v11

    .line 141
    iget-object v12, v15, Li2/h;->j:Li2/a;

    .line 142
    .line 143
    invoke-virtual {v12}, Li2/a;->I()Z

    .line 144
    .line 145
    .line 146
    move-result v12

    .line 147
    move-object/from16 v16, v13

    .line 148
    .line 149
    iget-object v13, v15, Li2/h;->j:Li2/a;

    .line 150
    .line 151
    invoke-virtual {v13}, Li2/a;->E()Z

    .line 152
    .line 153
    .line 154
    move-result v13

    .line 155
    move/from16 v21, v0

    .line 156
    .line 157
    iget-object v0, v15, Li2/h;->j:Li2/a;

    .line 158
    .line 159
    invoke-virtual {v0}, Li2/a;->p()Lq1/g;

    .line 160
    .line 161
    .line 162
    move-result-object v0

    .line 163
    move-object/from16 p1, v0

    .line 164
    .line 165
    iget-object v0, v15, Li2/h;->j:Li2/a;

    .line 166
    .line 167
    invoke-virtual {v0}, Li2/a;->C()Z

    .line 168
    .line 169
    .line 170
    move-result v0

    .line 171
    move/from16 p2, v0

    .line 172
    .line 173
    iget-object v0, v15, Li2/h;->j:Li2/a;

    .line 174
    .line 175
    invoke-virtual {v0}, Li2/a;->B()Z

    .line 176
    .line 177
    .line 178
    move-result v0

    .line 179
    move/from16 v17, v0

    .line 180
    .line 181
    iget-object v0, v15, Li2/h;->j:Li2/a;

    .line 182
    .line 183
    invoke-virtual {v0}, Li2/a;->A()Z

    .line 184
    .line 185
    .line 186
    move-result v0

    .line 187
    move/from16 v18, v0

    .line 188
    .line 189
    iget-object v0, v15, Li2/h;->j:Li2/a;

    .line 190
    .line 191
    invoke-virtual {v0}, Li2/a;->o()Z

    .line 192
    .line 193
    .line 194
    move-result v0

    .line 195
    move/from16 v19, v0

    .line 196
    .line 197
    iget-object v0, v15, Li2/h;->q:Ljava/util/concurrent/Executor;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    .line 198
    .line 199
    move-object/from16 v22, v16

    .line 200
    .line 201
    move-object/from16 v23, v14

    .line 202
    .line 203
    move-object/from16 v14, p1

    .line 204
    .line 205
    move/from16 v15, p2

    .line 206
    .line 207
    move/from16 v16, v17

    .line 208
    .line 209
    move/from16 v17, v18

    .line 210
    .line 211
    move/from16 v18, v19

    .line 212
    .line 213
    move-object/from16 v19, p0

    .line 214
    .line 215
    move-object/from16 v20, v0

    .line 216
    .line 217
    :try_start_1
    invoke-virtual/range {v1 .. v20}, Lcom/bumptech/glide/load/engine/j;->f(Lcom/bumptech/glide/e;Ljava/lang/Object;Lq1/e;IILjava/lang/Class;Ljava/lang/Class;Lcom/bumptech/glide/g;Lt1/a;Ljava/util/Map;ZZLq1/g;ZZZZLi2/g;Ljava/util/concurrent/Executor;)Lcom/bumptech/glide/load/engine/j$d;

    .line 218
    .line 219
    .line 220
    move-result-object v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 221
    move-object/from16 v1, p0

    .line 222
    .line 223
    :try_start_2
    iput-object v0, v1, Li2/h;->s:Lcom/bumptech/glide/load/engine/j$d;

    .line 224
    .line 225
    iget-object v0, v1, Li2/h;->v:Li2/h$a;

    .line 226
    .line 227
    move-object/from16 v2, v22

    .line 228
    .line 229
    if-eq v0, v2, :cond_3

    .line 230
    .line 231
    const/4 v0, 0x0

    .line 232
    iput-object v0, v1, Li2/h;->s:Lcom/bumptech/glide/load/engine/j$d;

    .line 233
    .line 234
    :cond_3
    if-eqz v21, :cond_4

    .line 235
    .line 236
    new-instance v0, Ljava/lang/StringBuilder;

    .line 237
    .line 238
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 239
    .line 240
    .line 241
    const-string v2, "finished onSizeReady in "

    .line 242
    .line 243
    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 244
    .line 245
    .line 246
    iget-wide v2, v1, Li2/h;->t:J

    .line 247
    .line 248
    invoke-static {v2, v3}, Lm2/f;->a(J)D

    .line 249
    .line 250
    .line 251
    move-result-wide v2

    .line 252
    invoke-virtual {v0, v2, v3}, Ljava/lang/StringBuilder;->append(D)Ljava/lang/StringBuilder;

    .line 253
    .line 254
    .line 255
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 256
    .line 257
    .line 258
    move-result-object v0

    .line 259
    invoke-direct {v1, v0}, Li2/h;->t(Ljava/lang/String;)V

    .line 260
    .line 261
    .line 262
    :cond_4
    monitor-exit v23

    .line 263
    return-void

    .line 264
    :catchall_0
    move-exception v0

    .line 265
    move-object/from16 v1, p0

    .line 266
    .line 267
    goto :goto_0

    .line 268
    :catchall_1
    move-exception v0

    .line 269
    move-object/from16 v23, v14

    .line 270
    .line 271
    move-object v1, v15

    .line 272
    :goto_0
    monitor-exit v23
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_2

    .line 273
    throw v0

    .line 274
    :catchall_2
    move-exception v0

    .line 275
    goto :goto_0
.end method

.method public f()Z
    .locals 3

    .line 1
    iget-object v0, p0, Li2/h;->c:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    iget-object v1, p0, Li2/h;->v:Li2/h$a;

    .line 5
    .line 6
    sget-object v2, Li2/h$a;->f:Li2/h$a;

    .line 7
    .line 8
    if-ne v1, v2, :cond_0

    .line 9
    .line 10
    const/4 v1, 0x1

    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v1, 0x0

    .line 13
    :goto_0
    monitor-exit v0

    .line 14
    return v1

    .line 15
    :catchall_0
    move-exception v1

    .line 16
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 17
    throw v1
.end method

.method public g()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, Li2/h;->b:Ln2/c;

    .line 2
    .line 3
    invoke-virtual {v0}, Ln2/c;->c()V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Li2/h;->c:Ljava/lang/Object;

    .line 7
    .line 8
    return-object v0
.end method

.method public h()V
    .locals 5

    .line 1
    iget-object v0, p0, Li2/h;->c:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    invoke-direct {p0}, Li2/h;->i()V

    .line 5
    .line 6
    .line 7
    iget-object v1, p0, Li2/h;->b:Ln2/c;

    .line 8
    .line 9
    invoke-virtual {v1}, Ln2/c;->c()V

    .line 10
    .line 11
    .line 12
    invoke-static {}, Lm2/f;->b()J

    .line 13
    .line 14
    .line 15
    move-result-wide v1

    .line 16
    iput-wide v1, p0, Li2/h;->t:J

    .line 17
    .line 18
    iget-object v1, p0, Li2/h;->h:Ljava/lang/Object;

    .line 19
    .line 20
    if-nez v1, :cond_2

    .line 21
    .line 22
    iget v1, p0, Li2/h;->k:I

    .line 23
    .line 24
    iget v2, p0, Li2/h;->l:I

    .line 25
    .line 26
    invoke-static {v1, v2}, Lm2/k;->r(II)Z

    .line 27
    .line 28
    .line 29
    move-result v1

    .line 30
    if-eqz v1, :cond_0

    .line 31
    .line 32
    iget v1, p0, Li2/h;->k:I

    .line 33
    .line 34
    iput v1, p0, Li2/h;->z:I

    .line 35
    .line 36
    iget v1, p0, Li2/h;->l:I

    .line 37
    .line 38
    iput v1, p0, Li2/h;->A:I

    .line 39
    .line 40
    :cond_0
    invoke-direct {p0}, Li2/h;->p()Landroid/graphics/drawable/Drawable;

    .line 41
    .line 42
    .line 43
    move-result-object v1

    .line 44
    if-nez v1, :cond_1

    .line 45
    .line 46
    const/4 v1, 0x5

    .line 47
    goto :goto_0

    .line 48
    :cond_1
    const/4 v1, 0x3

    .line 49
    :goto_0
    new-instance v2, Lcom/bumptech/glide/load/engine/GlideException;

    .line 50
    .line 51
    const-string v3, "Received null model"

    .line 52
    .line 53
    invoke-direct {v2, v3}, Lcom/bumptech/glide/load/engine/GlideException;-><init>(Ljava/lang/String;)V

    .line 54
    .line 55
    .line 56
    invoke-direct {p0, v2, v1}, Li2/h;->y(Lcom/bumptech/glide/load/engine/GlideException;I)V

    .line 57
    .line 58
    .line 59
    monitor-exit v0

    .line 60
    return-void

    .line 61
    :cond_2
    iget-object v1, p0, Li2/h;->v:Li2/h$a;

    .line 62
    .line 63
    sget-object v2, Li2/h$a;->b:Li2/h$a;

    .line 64
    .line 65
    if-eq v1, v2, :cond_8

    .line 66
    .line 67
    sget-object v3, Li2/h$a;->d:Li2/h$a;

    .line 68
    .line 69
    if-ne v1, v3, :cond_3

    .line 70
    .line 71
    iget-object v1, p0, Li2/h;->r:Lt1/c;

    .line 72
    .line 73
    sget-object v2, Lq1/a;->e:Lq1/a;

    .line 74
    .line 75
    invoke-virtual {p0, v1, v2}, Li2/h;->a(Lt1/c;Lq1/a;)V

    .line 76
    .line 77
    .line 78
    monitor-exit v0

    .line 79
    return-void

    .line 80
    :cond_3
    sget-object v1, Li2/h$a;->c:Li2/h$a;

    .line 81
    .line 82
    iput-object v1, p0, Li2/h;->v:Li2/h$a;

    .line 83
    .line 84
    iget v3, p0, Li2/h;->k:I

    .line 85
    .line 86
    iget v4, p0, Li2/h;->l:I

    .line 87
    .line 88
    invoke-static {v3, v4}, Lm2/k;->r(II)Z

    .line 89
    .line 90
    .line 91
    move-result v3

    .line 92
    if-eqz v3, :cond_4

    .line 93
    .line 94
    iget v3, p0, Li2/h;->k:I

    .line 95
    .line 96
    iget v4, p0, Li2/h;->l:I

    .line 97
    .line 98
    invoke-virtual {p0, v3, v4}, Li2/h;->e(II)V

    .line 99
    .line 100
    .line 101
    goto :goto_1

    .line 102
    :cond_4
    iget-object v3, p0, Li2/h;->n:Lj2/i;

    .line 103
    .line 104
    invoke-interface {v3, p0}, Lj2/i;->c(Lj2/h;)V

    .line 105
    .line 106
    .line 107
    :goto_1
    iget-object v3, p0, Li2/h;->v:Li2/h$a;

    .line 108
    .line 109
    if-eq v3, v2, :cond_5

    .line 110
    .line 111
    if-ne v3, v1, :cond_6

    .line 112
    .line 113
    :cond_5
    invoke-direct {p0}, Li2/h;->l()Z

    .line 114
    .line 115
    .line 116
    move-result v1

    .line 117
    if-eqz v1, :cond_6

    .line 118
    .line 119
    iget-object v1, p0, Li2/h;->n:Lj2/i;

    .line 120
    .line 121
    invoke-direct {p0}, Li2/h;->q()Landroid/graphics/drawable/Drawable;

    .line 122
    .line 123
    .line 124
    move-result-object v2

    .line 125
    invoke-interface {v1, v2}, Lj2/i;->h(Landroid/graphics/drawable/Drawable;)V

    .line 126
    .line 127
    .line 128
    :cond_6
    sget-boolean v1, Li2/h;->D:Z

    .line 129
    .line 130
    if-eqz v1, :cond_7

    .line 131
    .line 132
    new-instance v1, Ljava/lang/StringBuilder;

    .line 133
    .line 134
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 135
    .line 136
    .line 137
    const-string v2, "finished run method in "

    .line 138
    .line 139
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 140
    .line 141
    .line 142
    iget-wide v2, p0, Li2/h;->t:J

    .line 143
    .line 144
    invoke-static {v2, v3}, Lm2/f;->a(J)D

    .line 145
    .line 146
    .line 147
    move-result-wide v2

    .line 148
    invoke-virtual {v1, v2, v3}, Ljava/lang/StringBuilder;->append(D)Ljava/lang/StringBuilder;

    .line 149
    .line 150
    .line 151
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 152
    .line 153
    .line 154
    move-result-object v1

    .line 155
    invoke-direct {p0, v1}, Li2/h;->t(Ljava/lang/String;)V

    .line 156
    .line 157
    .line 158
    :cond_7
    monitor-exit v0

    .line 159
    return-void

    .line 160
    :cond_8
    new-instance v1, Ljava/lang/IllegalArgumentException;

    .line 161
    .line 162
    const-string v2, "Cannot restart a running request"

    .line 163
    .line 164
    invoke-direct {v1, v2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 165
    .line 166
    .line 167
    throw v1

    .line 168
    :catchall_0
    move-exception v1

    .line 169
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 170
    throw v1
.end method

.method public isRunning()Z
    .locals 3

    .line 1
    iget-object v0, p0, Li2/h;->c:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    iget-object v1, p0, Li2/h;->v:Li2/h$a;

    .line 5
    .line 6
    sget-object v2, Li2/h$a;->b:Li2/h$a;

    .line 7
    .line 8
    if-eq v1, v2, :cond_1

    .line 9
    .line 10
    sget-object v2, Li2/h$a;->c:Li2/h$a;

    .line 11
    .line 12
    if-ne v1, v2, :cond_0

    .line 13
    .line 14
    goto :goto_0

    .line 15
    :cond_0
    const/4 v1, 0x0

    .line 16
    goto :goto_1

    .line 17
    :cond_1
    :goto_0
    const/4 v1, 0x1

    .line 18
    :goto_1
    monitor-exit v0

    .line 19
    return v1

    .line 20
    :catchall_0
    move-exception v1

    .line 21
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 22
    throw v1
.end method

.method public j()Z
    .locals 3

    .line 1
    iget-object v0, p0, Li2/h;->c:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    iget-object v1, p0, Li2/h;->v:Li2/h$a;

    .line 5
    .line 6
    sget-object v2, Li2/h$a;->d:Li2/h$a;

    .line 7
    .line 8
    if-ne v1, v2, :cond_0

    .line 9
    .line 10
    const/4 v1, 0x1

    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v1, 0x0

    .line 13
    :goto_0
    monitor-exit v0

    .line 14
    return v1

    .line 15
    :catchall_0
    move-exception v1

    .line 16
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 17
    throw v1
.end method

.method public pause()V
    .locals 2

    .line 1
    iget-object v0, p0, Li2/h;->c:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    invoke-virtual {p0}, Li2/h;->isRunning()Z

    .line 5
    .line 6
    .line 7
    move-result v1

    .line 8
    if-eqz v1, :cond_0

    .line 9
    .line 10
    invoke-virtual {p0}, Li2/h;->clear()V

    .line 11
    .line 12
    .line 13
    :cond_0
    monitor-exit v0

    .line 14
    return-void

    .line 15
    :catchall_0
    move-exception v1

    .line 16
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 17
    throw v1
.end method
