.class public Lg0/b;
.super Lg0/d;
.source "FastOutSlowInInterpolator.java"


# static fields
.field private static final c:[F


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    const/16 v0, 0xc9

    .line 2
    .line 3
    new-array v0, v0, [F

    .line 4
    .line 5
    fill-array-data v0, :array_0

    .line 6
    .line 7
    .line 8
    sput-object v0, Lg0/b;->c:[F

    .line 9
    .line 10
    return-void

    .line 11
    :array_0
    .array-data 4
        0x0
        0x38d1b717    # 1.0E-4f
        0x3951b717    # 2.0E-4f
        0x3a03126f    # 5.0E-4f
        0x3a6bedfa    # 9.0E-4f
        0x3ab78034    # 0.0014f
        0x3b03126f    # 0.002f
        0x3b30f27c    # 0.0027f
        0x3b6bedfa    # 0.0036f
        0x3b96bb99    # 0.0046f
        0x3bbe0ded    # 0.0058f
        0x3be8a71e    # 0.0071f
        0x3c0b4396    # 0.0085f
        0x3c257a78    # 0.0101f
        0x3c4154ca    # 0.0118f
        0x3c6075f7    # 0.0137f
        0x3c816f00    # 0.0158f
        0x3c9374bc    # 0.018f
        0x3ca7ef9e    # 0.0205f
        0x3cbd3c36    # 0.0231f
        0x3cd42c3d    # 0.0259f
        0x3cecbfb1    # 0.0289f
        0x3d037b4a
        0x3d116873    # 0.0355f
        0x3d202752    # 0.0391f
        0x3d3020c5    # 0.043f
        0x3d40ebee    # 0.0471f
        0x3d5288ce    # 0.0514f
        0x3d656042    # 0.056f
        0x3d79096c    # 0.0608f
        0x3d872b02    # 0.066f
        0x3d923a2a    # 0.0714f
        0x3d9de69b    # 0.0771f
        0x3da9fbe7    # 0.083f
        0x3db6e2eb    # 0.0893f
        0x3dc46738    # 0.0959f
        0x3dd2bd3c    # 0.1029f
        0x3de17c1c    # 0.1101f
        0x3df10cb3    # 0.1177f
        0x3e00b780    # 0.1257f
        0x3e091d15    # 0.1339f
        0x3e1205bc    # 0.1426f
        0x3e1b3d08    # 0.1516f
        0x3e24dd2f    # 0.161f
        0x3e2ecbfb    # 0.1707f
        0x3e3923a3    # 0.1808f
        0x3e43e426    # 0.1913f
        0x3e4ef34d    # 0.2021f
        0x3e5a6b51    # 0.2133f
        0x3e6631f9    # 0.2248f
        0x3e724745    # 0.2366f
        0x3e7eab36    # 0.2487f
        0x3e85aee6    # 0.2611f
        0x3e8c2f83    # 0.2738f
        0x3e92ca58    # 0.2867f
        0x3e997f63    # 0.2998f
        0x3ea04ea5    # 0.3131f
        0x3ea72b02    # 0.3265f
        0x3eae147b    # 0.34f
        0x3eb50b0f    # 0.3536f
        0x3ebc0ebf    # 0.3673f
        0x3ec3126f    # 0.381f
        0x3eca0903    # 0.3946f
        0x3ed0ff97    # 0.4082f
        0x3ed7e910    # 0.4217f
        0x3eded289    # 0.4352f
        0x3ee5a1cb    # 0.4485f
        0x3eec56d6    # 0.4616f
        0x3ef2fec5    # 0.4746f
        0x3ef98c7e    # 0.4874f
        0x3f000000    # 0.5f
        0x3f032ca5    # 0.5124f
        0x3f064c30    # 0.5246f
        0x3f095810    # 0.5365f
        0x3f0c56d6    # 0.5482f
        0x3f0f4880    # 0.5597f
        0x3f122d0e    # 0.571f
        0x3f14fdf4    # 0.582f
        0x3f17c1be    # 0.5928f
        0x3f1a71de    # 0.6033f
        0x3f1d14e4    # 0.6136f
        0x3f1faace    # 0.6237f
        0x3f222d0e    # 0.6335f
        0x3f24a234    # 0.6431f
        0x3f270a3d    # 0.6525f
        0x3f295e9e    # 0.6616f
        0x3f2bac71    # 0.6706f
        0x3f2de69b    # 0.6793f
        0x3f3013a9    # 0.6878f
        0x3f32339c    # 0.6961f
        0x3f344d01    # 0.7043f
        0x3f3652bd    # 0.7122f
        0x3f384b5e    # 0.7199f
        0x3f3a3d71    # 0.7275f
        0x3f3c2268    # 0.7349f
        0x3f3dfa44    # 0.7421f
        0x3f3fc505    # 0.7491f
        0x3f4182aa    # 0.7559f
        0x3f4339c1    # 0.7626f
        0x3f44ea4b    # 0.7692f
        0x3f468db9    # 0.7756f
        0x3f48240b    # 0.7818f
        0x3f49b3d0    # 0.7879f
        0x3f4b367a    # 0.7938f
        0x3f4cb296    # 0.7996f
        0x3f4e2824    # 0.8053f
        0x3f4f9097    # 0.8108f
        0x3f50f27c    # 0.8162f
        0x3f524dd3    # 0.8215f
        0x3f539c0f    # 0.8266f
        0x3f54ea4b    # 0.8317f
        0x3f562b6b    # 0.8366f
        0x3f5765fe    # 0.8414f
        0x3f589a02    # 0.8461f
        0x3f59c77a    # 0.8507f
        0x3f5ae7d5    # 0.8551f
        0x3f5c0831    # 0.8595f
        0x3f5d21ff    # 0.8638f
        0x3f5e2eb2    # 0.8679f
        0x3f5f3b64    # 0.872f
        0x3f604189    # 0.876f
        0x3f613a93    # 0.8798f
        0x3f62339c    # 0.8836f
        0x3f632618    # 0.8873f
        0x3f641206    # 0.8909f
        0x3f64fdf4    # 0.8945f
        0x3f65dcc6    # 0.8979f
        0x3f66bb99    # 0.9013f
        0x3f6793de    # 0.9046f
        0x3f686595    # 0.9078f
        0x3f6930be    # 0.9109f
        0x3f69f55a    # 0.9139f
        0x3f6ab9f5    # 0.9169f
        0x3f6b7803    # 0.9198f
        0x3f6c3611    # 0.9227f
        0x3f6ce704    # 0.9254f
        0x3f6d97f6    # 0.9281f
        0x3f6e425b    # 0.9307f
        0x3f6eecc0    # 0.9333f
        0x3f6f9097    # 0.9358f
        0x3f702de0    # 0.9382f
        0x3f70cb29    # 0.9406f
        0x3f7161e5    # 0.9429f
        0x3f71f8a1    # 0.9452f
        0x3f7288ce    # 0.9474f
        0x3f73126f    # 0.9495f
        0x3f739c0f    # 0.9516f
        0x3f741f21    # 0.9536f
        0x3f74a234    # 0.9556f
        0x3f751eb8    # 0.9575f
        0x3f759b3d    # 0.9594f
        0x3f761134    # 0.9612f
        0x3f76809d    # 0.9629f
        0x3f76f007    # 0.9646f
        0x3f775f70    # 0.9663f
        0x3f77c84b    # 0.9679f
        0x3f783127    # 0.9695f
        0x3f789375    # 0.971f
        0x3f78f5c3    # 0.9725f
        0x3f795183    # 0.9739f
        0x3f79ad43    # 0.9753f
        0x3f7a0275    # 0.9766f
        0x3f7a57a8    # 0.9779f
        0x3f7aa64c    # 0.9791f
        0x3f7af4f1    # 0.9803f
        0x3f7b4396    # 0.9815f
        0x3f7b8bac    # 0.9826f
        0x3f7bd3c3    # 0.9837f
        0x3f7c1bda    # 0.9848f
        0x3f7c5d64    # 0.9858f
        0x3f7c985f    # 0.9867f
        0x3f7cd9e8    # 0.9877f
        0x3f7d0e56    # 0.9885f
        0x3f7d4952    # 0.9894f
        0x3f7d7dbf    # 0.9902f
        0x3f7db22d    # 0.991f
        0x3f7de00d    # 0.9917f
        0x3f7e0ded    # 0.9924f
        0x3f7e3bcd    # 0.9931f
        0x3f7e6320    # 0.9937f
        0x3f7e9100    # 0.9944f
        0x3f7eb1c4    # 0.9949f
        0x3f7ed917    # 0.9955f
        0x3f7ef9db    # 0.996f
        0x3f7f1412    # 0.9964f
        0x3f7f34d7    # 0.9969f
        0x3f7f4f0e    # 0.9973f
        0x3f7f6944    # 0.9977f
        0x3f7f7cee    # 0.998f
        0x3f7f9724    # 0.9984f
        0x3f7fa440    # 0.9986f
        0x3f7fb7e9    # 0.9989f
        0x3f7fc505    # 0.9991f
        0x3f7fd220    # 0.9993f
        0x3f7fdf3b    # 0.9995f
        0x3f7fec57    # 0.9997f
        0x3f7ff2e5    # 0.9998f
        0x3f7ff972    # 0.9999f
        0x3f7ff972    # 0.9999f
        0x3f800000    # 1.0f
        0x3f800000    # 1.0f
    .end array-data
.end method

.method public constructor <init>()V
    .locals 1

    .line 1
    sget-object v0, Lg0/b;->c:[F

    .line 2
    .line 3
    invoke-direct {p0, v0}, Lg0/d;-><init>([F)V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public bridge synthetic getInterpolation(F)F
    .locals 0

    .line 1
    invoke-super {p0, p1}, Lg0/d;->getInterpolation(F)F

    .line 2
    .line 3
    .line 4
    move-result p1

    .line 5
    return p1
.end method
