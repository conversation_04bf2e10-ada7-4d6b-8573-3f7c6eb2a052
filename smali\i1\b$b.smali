.class public final Li1/b$b;
.super Ljava/lang/Object;
.source "Extensions.kt"

# interfaces
.implements Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Li1/b;->j(Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field final synthetic a:Landroid/view/View;

.field final synthetic b:Landroidx/appcompat/widget/Toolbar;

.field final synthetic c:Li1/b;

.field final synthetic d:Z


# direct methods
.method public constructor <init>(Landroid/view/View;Landroidx/appcompat/widget/Toolbar;Li1/b;Z)V
    .locals 0

    .line 1
    iput-object p1, p0, Li1/b$b;->a:Landroid/view/View;

    .line 2
    .line 3
    iput-object p2, p0, Li1/b$b;->b:Landroidx/appcompat/widget/Toolbar;

    .line 4
    .line 5
    iput-object p3, p0, Li1/b$b;->c:Li1/b;

    .line 6
    .line 7
    iput-boolean p4, p0, Li1/b$b;->d:Z

    .line 8
    .line 9
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 10
    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public onGlobalLayout()V
    .locals 4

    .line 1
    iget-object v0, p0, Li1/b$b;->c:Li1/b;

    .line 2
    .line 3
    invoke-virtual {v0}, Li1/b;->f()Ljb/p;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    iget-object v1, p0, Li1/b$b;->b:Landroidx/appcompat/widget/Toolbar;

    .line 10
    .line 11
    invoke-virtual {v1}, Landroid/view/View;->animate()Landroid/view/ViewPropertyAnimator;

    .line 12
    .line 13
    .line 14
    move-result-object v2

    .line 15
    const-string v3, "animate()"

    .line 16
    .line 17
    invoke-static {v2, v3}, Lkb/l;->d(Ljava/lang/Object;Ljava/lang/String;)V

    .line 18
    .line 19
    .line 20
    invoke-interface {v0, v1, v2}, Ljb/p;->p(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 21
    .line 22
    .line 23
    move-result-object v0

    .line 24
    check-cast v0, Lwa/q;

    .line 25
    .line 26
    :cond_0
    iget-object v0, p0, Li1/b$b;->a:Landroid/view/View;

    .line 27
    .line 28
    invoke-virtual {v0}, Landroid/view/View;->getViewTreeObserver()Landroid/view/ViewTreeObserver;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    invoke-virtual {v0, p0}, Landroid/view/ViewTreeObserver;->removeGlobalOnLayoutListener(Landroid/view/ViewTreeObserver$OnGlobalLayoutListener;)V

    .line 33
    .line 34
    .line 35
    return-void
.end method
