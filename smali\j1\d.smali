.class Lj1/d;
.super Ljava/lang/Object;
.source "DialogInit.java"


# direct methods
.method private static a(Landroid/widget/ProgressBar;)V
    .locals 0

    .line 1
    return-void
.end method

.method static b(Lj1/f$d;)I
    .locals 2

    .line 1
    iget-object v0, p0, Lj1/f$d;->s:Landroid/view/View;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    sget p0, Lj1/l;->c:I

    .line 6
    .line 7
    return p0

    .line 8
    :cond_0
    iget-object v0, p0, Lj1/f$d;->l:Ljava/util/ArrayList;

    .line 9
    .line 10
    if-nez v0, :cond_6

    .line 11
    .line 12
    iget-object v0, p0, Lj1/f$d;->S:Landroidx/recyclerview/widget/RecyclerView$h;

    .line 13
    .line 14
    if-eqz v0, :cond_1

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_1
    iget v0, p0, Lj1/f$d;->f0:I

    .line 18
    .line 19
    const/4 v1, -0x2

    .line 20
    if-le v0, v1, :cond_2

    .line 21
    .line 22
    sget p0, Lj1/l;->f:I

    .line 23
    .line 24
    return p0

    .line 25
    :cond_2
    iget-boolean v0, p0, Lj1/f$d;->d0:Z

    .line 26
    .line 27
    if-eqz v0, :cond_4

    .line 28
    .line 29
    iget-boolean p0, p0, Lj1/f$d;->v0:Z

    .line 30
    .line 31
    if-eqz p0, :cond_3

    .line 32
    .line 33
    sget p0, Lj1/l;->h:I

    .line 34
    .line 35
    return p0

    .line 36
    :cond_3
    sget p0, Lj1/l;->g:I

    .line 37
    .line 38
    return p0

    .line 39
    :cond_4
    iget-object p0, p0, Lj1/f$d;->q0:Ljava/lang/CharSequence;

    .line 40
    .line 41
    if-eqz p0, :cond_5

    .line 42
    .line 43
    sget p0, Lj1/l;->b:I

    .line 44
    .line 45
    return p0

    .line 46
    :cond_5
    sget p0, Lj1/l;->a:I

    .line 47
    .line 48
    return p0

    .line 49
    :cond_6
    :goto_0
    iget-object p0, p0, Lj1/f$d;->q0:Ljava/lang/CharSequence;

    .line 50
    .line 51
    if-eqz p0, :cond_7

    .line 52
    .line 53
    sget p0, Lj1/l;->e:I

    .line 54
    .line 55
    return p0

    .line 56
    :cond_7
    sget p0, Lj1/l;->d:I

    .line 57
    .line 58
    return p0
.end method

.method static c(Lj1/f$d;)I
    .locals 4

    .line 1
    iget-object v0, p0, Lj1/f$d;->a:Landroid/content/Context;

    .line 2
    .line 3
    sget v1, Lj1/g;->o:I

    .line 4
    .line 5
    iget-object v2, p0, Lj1/f$d;->F:Lj1/p;

    .line 6
    .line 7
    sget-object v3, Lj1/p;->b:Lj1/p;

    .line 8
    .line 9
    if-ne v2, v3, :cond_0

    .line 10
    .line 11
    const/4 v2, 0x1

    .line 12
    goto :goto_0

    .line 13
    :cond_0
    const/4 v2, 0x0

    .line 14
    :goto_0
    invoke-static {v0, v1, v2}, Ll1/a;->k(Landroid/content/Context;IZ)Z

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    if-eqz v0, :cond_1

    .line 19
    .line 20
    goto :goto_1

    .line 21
    :cond_1
    sget-object v3, Lj1/p;->a:Lj1/p;

    .line 22
    .line 23
    :goto_1
    iput-object v3, p0, Lj1/f$d;->F:Lj1/p;

    .line 24
    .line 25
    if-eqz v0, :cond_2

    .line 26
    .line 27
    sget p0, Lj1/m;->a:I

    .line 28
    .line 29
    goto :goto_2

    .line 30
    :cond_2
    sget p0, Lj1/m;->b:I

    .line 31
    .line 32
    :goto_2
    return p0
.end method

.method static d(Lj1/f;)V
    .locals 11

    .line 1
    iget-object v0, p0, Lj1/f;->c:Lj1/f$d;

    .line 2
    .line 3
    iget-boolean v1, v0, Lj1/f$d;->G:Z

    .line 4
    .line 5
    invoke-virtual {p0, v1}, Landroid/app/Dialog;->setCancelable(Z)V

    .line 6
    .line 7
    .line 8
    iget-boolean v1, v0, Lj1/f$d;->H:Z

    .line 9
    .line 10
    invoke-virtual {p0, v1}, Landroid/app/Dialog;->setCanceledOnTouchOutside(Z)V

    .line 11
    .line 12
    .line 13
    iget v1, v0, Lj1/f$d;->b0:I

    .line 14
    .line 15
    if-nez v1, :cond_0

    .line 16
    .line 17
    iget-object v1, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 18
    .line 19
    sget v2, Lj1/g;->e:I

    .line 20
    .line 21
    invoke-virtual {p0}, Landroid/app/Dialog;->getContext()Landroid/content/Context;

    .line 22
    .line 23
    .line 24
    move-result-object v3

    .line 25
    sget v4, Lj1/g;->b:I

    .line 26
    .line 27
    invoke-static {v3, v4}, Ll1/a;->l(Landroid/content/Context;I)I

    .line 28
    .line 29
    .line 30
    move-result v3

    .line 31
    invoke-static {v1, v2, v3}, Ll1/a;->m(Landroid/content/Context;II)I

    .line 32
    .line 33
    .line 34
    move-result v1

    .line 35
    iput v1, v0, Lj1/f$d;->b0:I

    .line 36
    .line 37
    :cond_0
    iget v1, v0, Lj1/f$d;->b0:I

    .line 38
    .line 39
    if-eqz v1, :cond_1

    .line 40
    .line 41
    new-instance v1, Landroid/graphics/drawable/GradientDrawable;

    .line 42
    .line 43
    invoke-direct {v1}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 44
    .line 45
    .line 46
    iget-object v2, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 47
    .line 48
    invoke-virtual {v2}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 49
    .line 50
    .line 51
    move-result-object v2

    .line 52
    sget v3, Lj1/i;->a:I

    .line 53
    .line 54
    invoke-virtual {v2, v3}, Landroid/content/res/Resources;->getDimension(I)F

    .line 55
    .line 56
    .line 57
    move-result v2

    .line 58
    invoke-virtual {v1, v2}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 59
    .line 60
    .line 61
    iget v2, v0, Lj1/f$d;->b0:I

    .line 62
    .line 63
    invoke-virtual {v1, v2}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 64
    .line 65
    .line 66
    invoke-virtual {p0}, Landroid/app/Dialog;->getWindow()Landroid/view/Window;

    .line 67
    .line 68
    .line 69
    move-result-object v2

    .line 70
    invoke-virtual {v2, v1}, Landroid/view/Window;->setBackgroundDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 71
    .line 72
    .line 73
    :cond_1
    iget-boolean v1, v0, Lj1/f$d;->z0:Z

    .line 74
    .line 75
    if-nez v1, :cond_2

    .line 76
    .line 77
    iget-object v1, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 78
    .line 79
    sget v2, Lj1/g;->B:I

    .line 80
    .line 81
    iget-object v3, v0, Lj1/f$d;->v:Landroid/content/res/ColorStateList;

    .line 82
    .line 83
    invoke-static {v1, v2, v3}, Ll1/a;->i(Landroid/content/Context;ILandroid/content/res/ColorStateList;)Landroid/content/res/ColorStateList;

    .line 84
    .line 85
    .line 86
    move-result-object v1

    .line 87
    iput-object v1, v0, Lj1/f$d;->v:Landroid/content/res/ColorStateList;

    .line 88
    .line 89
    :cond_2
    iget-boolean v1, v0, Lj1/f$d;->A0:Z

    .line 90
    .line 91
    if-nez v1, :cond_3

    .line 92
    .line 93
    iget-object v1, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 94
    .line 95
    sget v2, Lj1/g;->A:I

    .line 96
    .line 97
    iget-object v3, v0, Lj1/f$d;->x:Landroid/content/res/ColorStateList;

    .line 98
    .line 99
    invoke-static {v1, v2, v3}, Ll1/a;->i(Landroid/content/Context;ILandroid/content/res/ColorStateList;)Landroid/content/res/ColorStateList;

    .line 100
    .line 101
    .line 102
    move-result-object v1

    .line 103
    iput-object v1, v0, Lj1/f$d;->x:Landroid/content/res/ColorStateList;

    .line 104
    .line 105
    :cond_3
    iget-boolean v1, v0, Lj1/f$d;->B0:Z

    .line 106
    .line 107
    if-nez v1, :cond_4

    .line 108
    .line 109
    iget-object v1, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 110
    .line 111
    sget v2, Lj1/g;->z:I

    .line 112
    .line 113
    iget-object v3, v0, Lj1/f$d;->w:Landroid/content/res/ColorStateList;

    .line 114
    .line 115
    invoke-static {v1, v2, v3}, Ll1/a;->i(Landroid/content/Context;ILandroid/content/res/ColorStateList;)Landroid/content/res/ColorStateList;

    .line 116
    .line 117
    .line 118
    move-result-object v1

    .line 119
    iput-object v1, v0, Lj1/f$d;->w:Landroid/content/res/ColorStateList;

    .line 120
    .line 121
    :cond_4
    iget-boolean v1, v0, Lj1/f$d;->C0:Z

    .line 122
    .line 123
    if-nez v1, :cond_5

    .line 124
    .line 125
    iget-object v1, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 126
    .line 127
    sget v2, Lj1/g;->F:I

    .line 128
    .line 129
    iget v3, v0, Lj1/f$d;->t:I

    .line 130
    .line 131
    invoke-static {v1, v2, v3}, Ll1/a;->m(Landroid/content/Context;II)I

    .line 132
    .line 133
    .line 134
    move-result v1

    .line 135
    iput v1, v0, Lj1/f$d;->t:I

    .line 136
    .line 137
    :cond_5
    iget-boolean v1, v0, Lj1/f$d;->w0:Z

    .line 138
    .line 139
    const v2, 0x1010036

    .line 140
    .line 141
    .line 142
    if-nez v1, :cond_6

    .line 143
    .line 144
    invoke-virtual {p0}, Landroid/app/Dialog;->getContext()Landroid/content/Context;

    .line 145
    .line 146
    .line 147
    move-result-object v1

    .line 148
    invoke-static {v1, v2}, Ll1/a;->l(Landroid/content/Context;I)I

    .line 149
    .line 150
    .line 151
    move-result v1

    .line 152
    iget-object v3, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 153
    .line 154
    sget v4, Lj1/g;->D:I

    .line 155
    .line 156
    invoke-static {v3, v4, v1}, Ll1/a;->m(Landroid/content/Context;II)I

    .line 157
    .line 158
    .line 159
    move-result v1

    .line 160
    iput v1, v0, Lj1/f$d;->i:I

    .line 161
    .line 162
    :cond_6
    iget-boolean v1, v0, Lj1/f$d;->x0:Z

    .line 163
    .line 164
    if-nez v1, :cond_7

    .line 165
    .line 166
    invoke-virtual {p0}, Landroid/app/Dialog;->getContext()Landroid/content/Context;

    .line 167
    .line 168
    .line 169
    move-result-object v1

    .line 170
    const v3, 0x1010038

    .line 171
    .line 172
    .line 173
    invoke-static {v1, v3}, Ll1/a;->l(Landroid/content/Context;I)I

    .line 174
    .line 175
    .line 176
    move-result v1

    .line 177
    iget-object v3, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 178
    .line 179
    sget v4, Lj1/g;->m:I

    .line 180
    .line 181
    invoke-static {v3, v4, v1}, Ll1/a;->m(Landroid/content/Context;II)I

    .line 182
    .line 183
    .line 184
    move-result v1

    .line 185
    iput v1, v0, Lj1/f$d;->j:I

    .line 186
    .line 187
    :cond_7
    iget-boolean v1, v0, Lj1/f$d;->y0:Z

    .line 188
    .line 189
    if-nez v1, :cond_8

    .line 190
    .line 191
    iget-object v1, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 192
    .line 193
    sget v3, Lj1/g;->u:I

    .line 194
    .line 195
    iget v4, v0, Lj1/f$d;->j:I

    .line 196
    .line 197
    invoke-static {v1, v3, v4}, Ll1/a;->m(Landroid/content/Context;II)I

    .line 198
    .line 199
    .line 200
    move-result v1

    .line 201
    iput v1, v0, Lj1/f$d;->c0:I

    .line 202
    .line 203
    :cond_8
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 204
    .line 205
    sget v3, Lj1/k;->m:I

    .line 206
    .line 207
    invoke-virtual {v1, v3}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 208
    .line 209
    .line 210
    move-result-object v1

    .line 211
    check-cast v1, Landroid/widget/TextView;

    .line 212
    .line 213
    iput-object v1, p0, Lj1/f;->f:Landroid/widget/TextView;

    .line 214
    .line 215
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 216
    .line 217
    sget v3, Lj1/k;->h:I

    .line 218
    .line 219
    invoke-virtual {v1, v3}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 220
    .line 221
    .line 222
    move-result-object v1

    .line 223
    check-cast v1, Landroid/widget/ImageView;

    .line 224
    .line 225
    iput-object v1, p0, Lj1/f;->e:Landroid/widget/ImageView;

    .line 226
    .line 227
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 228
    .line 229
    sget v3, Lj1/k;->n:I

    .line 230
    .line 231
    invoke-virtual {v1, v3}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 232
    .line 233
    .line 234
    move-result-object v1

    .line 235
    iput-object v1, p0, Lj1/f;->j:Landroid/view/View;

    .line 236
    .line 237
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 238
    .line 239
    sget v3, Lj1/k;->d:I

    .line 240
    .line 241
    invoke-virtual {v1, v3}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 242
    .line 243
    .line 244
    move-result-object v1

    .line 245
    check-cast v1, Landroid/widget/TextView;

    .line 246
    .line 247
    iput-object v1, p0, Lj1/f;->g:Landroid/widget/TextView;

    .line 248
    .line 249
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 250
    .line 251
    sget v3, Lj1/k;->e:I

    .line 252
    .line 253
    invoke-virtual {v1, v3}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 254
    .line 255
    .line 256
    move-result-object v1

    .line 257
    check-cast v1, Landroidx/recyclerview/widget/RecyclerView;

    .line 258
    .line 259
    iput-object v1, p0, Lj1/f;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 260
    .line 261
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 262
    .line 263
    sget v3, Lj1/k;->k:I

    .line 264
    .line 265
    invoke-virtual {v1, v3}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 266
    .line 267
    .line 268
    move-result-object v1

    .line 269
    check-cast v1, Landroid/widget/CheckBox;

    .line 270
    .line 271
    iput-object v1, p0, Lj1/f;->u:Landroid/widget/CheckBox;

    .line 272
    .line 273
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 274
    .line 275
    sget v3, Lj1/k;->c:I

    .line 276
    .line 277
    invoke-virtual {v1, v3}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 278
    .line 279
    .line 280
    move-result-object v1

    .line 281
    check-cast v1, Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 282
    .line 283
    iput-object v1, p0, Lj1/f;->v:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 284
    .line 285
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 286
    .line 287
    sget v3, Lj1/k;->b:I

    .line 288
    .line 289
    invoke-virtual {v1, v3}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 290
    .line 291
    .line 292
    move-result-object v1

    .line 293
    check-cast v1, Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 294
    .line 295
    iput-object v1, p0, Lj1/f;->w:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 296
    .line 297
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 298
    .line 299
    sget v3, Lj1/k;->a:I

    .line 300
    .line 301
    invoke-virtual {v1, v3}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 302
    .line 303
    .line 304
    move-result-object v1

    .line 305
    check-cast v1, Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 306
    .line 307
    iput-object v1, p0, Lj1/f;->x:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 308
    .line 309
    iget-object v1, p0, Lj1/f;->v:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 310
    .line 311
    iget-object v3, v0, Lj1/f$d;->m:Ljava/lang/CharSequence;

    .line 312
    .line 313
    const/16 v4, 0x8

    .line 314
    .line 315
    const/4 v5, 0x0

    .line 316
    if-eqz v3, :cond_9

    .line 317
    .line 318
    const/4 v3, 0x0

    .line 319
    goto :goto_0

    .line 320
    :cond_9
    const/16 v3, 0x8

    .line 321
    .line 322
    :goto_0
    invoke-virtual {v1, v3}, Landroid/view/View;->setVisibility(I)V

    .line 323
    .line 324
    .line 325
    iget-object v1, p0, Lj1/f;->w:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 326
    .line 327
    iget-object v3, v0, Lj1/f$d;->n:Ljava/lang/CharSequence;

    .line 328
    .line 329
    if-eqz v3, :cond_a

    .line 330
    .line 331
    const/4 v3, 0x0

    .line 332
    goto :goto_1

    .line 333
    :cond_a
    const/16 v3, 0x8

    .line 334
    .line 335
    :goto_1
    invoke-virtual {v1, v3}, Landroid/view/View;->setVisibility(I)V

    .line 336
    .line 337
    .line 338
    iget-object v1, p0, Lj1/f;->x:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 339
    .line 340
    iget-object v3, v0, Lj1/f$d;->o:Ljava/lang/CharSequence;

    .line 341
    .line 342
    if-eqz v3, :cond_b

    .line 343
    .line 344
    const/4 v3, 0x0

    .line 345
    goto :goto_2

    .line 346
    :cond_b
    const/16 v3, 0x8

    .line 347
    .line 348
    :goto_2
    invoke-virtual {v1, v3}, Landroid/view/View;->setVisibility(I)V

    .line 349
    .line 350
    .line 351
    iget-object v1, p0, Lj1/f;->v:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 352
    .line 353
    const/4 v3, 0x1

    .line 354
    invoke-virtual {v1, v3}, Landroid/view/View;->setFocusable(Z)V

    .line 355
    .line 356
    .line 357
    iget-object v1, p0, Lj1/f;->w:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 358
    .line 359
    invoke-virtual {v1, v3}, Landroid/view/View;->setFocusable(Z)V

    .line 360
    .line 361
    .line 362
    iget-object v1, p0, Lj1/f;->x:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 363
    .line 364
    invoke-virtual {v1, v3}, Landroid/view/View;->setFocusable(Z)V

    .line 365
    .line 366
    .line 367
    iget-boolean v1, v0, Lj1/f$d;->p:Z

    .line 368
    .line 369
    if-eqz v1, :cond_c

    .line 370
    .line 371
    iget-object v1, p0, Lj1/f;->v:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 372
    .line 373
    invoke-virtual {v1}, Landroid/view/View;->requestFocus()Z

    .line 374
    .line 375
    .line 376
    :cond_c
    iget-boolean v1, v0, Lj1/f$d;->q:Z

    .line 377
    .line 378
    if-eqz v1, :cond_d

    .line 379
    .line 380
    iget-object v1, p0, Lj1/f;->w:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 381
    .line 382
    invoke-virtual {v1}, Landroid/view/View;->requestFocus()Z

    .line 383
    .line 384
    .line 385
    :cond_d
    iget-boolean v1, v0, Lj1/f$d;->r:Z

    .line 386
    .line 387
    if-eqz v1, :cond_e

    .line 388
    .line 389
    iget-object v1, p0, Lj1/f;->x:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 390
    .line 391
    invoke-virtual {v1}, Landroid/view/View;->requestFocus()Z

    .line 392
    .line 393
    .line 394
    :cond_e
    iget-object v1, v0, Lj1/f$d;->P:Landroid/graphics/drawable/Drawable;

    .line 395
    .line 396
    if-eqz v1, :cond_f

    .line 397
    .line 398
    iget-object v1, p0, Lj1/f;->e:Landroid/widget/ImageView;

    .line 399
    .line 400
    invoke-virtual {v1, v5}, Landroid/widget/ImageView;->setVisibility(I)V

    .line 401
    .line 402
    .line 403
    iget-object v1, p0, Lj1/f;->e:Landroid/widget/ImageView;

    .line 404
    .line 405
    iget-object v6, v0, Lj1/f$d;->P:Landroid/graphics/drawable/Drawable;

    .line 406
    .line 407
    invoke-virtual {v1, v6}, Landroid/widget/ImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 408
    .line 409
    .line 410
    goto :goto_3

    .line 411
    :cond_f
    iget-object v1, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 412
    .line 413
    sget v6, Lj1/g;->r:I

    .line 414
    .line 415
    invoke-static {v1, v6}, Ll1/a;->p(Landroid/content/Context;I)Landroid/graphics/drawable/Drawable;

    .line 416
    .line 417
    .line 418
    move-result-object v1

    .line 419
    if-eqz v1, :cond_10

    .line 420
    .line 421
    iget-object v6, p0, Lj1/f;->e:Landroid/widget/ImageView;

    .line 422
    .line 423
    invoke-virtual {v6, v5}, Landroid/widget/ImageView;->setVisibility(I)V

    .line 424
    .line 425
    .line 426
    iget-object v6, p0, Lj1/f;->e:Landroid/widget/ImageView;

    .line 427
    .line 428
    invoke-virtual {v6, v1}, Landroid/widget/ImageView;->setImageDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 429
    .line 430
    .line 431
    goto :goto_3

    .line 432
    :cond_10
    iget-object v1, p0, Lj1/f;->e:Landroid/widget/ImageView;

    .line 433
    .line 434
    invoke-virtual {v1, v4}, Landroid/widget/ImageView;->setVisibility(I)V

    .line 435
    .line 436
    .line 437
    :goto_3
    iget v1, v0, Lj1/f$d;->R:I

    .line 438
    .line 439
    const/4 v6, -0x1

    .line 440
    if-ne v1, v6, :cond_11

    .line 441
    .line 442
    iget-object v1, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 443
    .line 444
    sget v7, Lj1/g;->t:I

    .line 445
    .line 446
    invoke-static {v1, v7}, Ll1/a;->n(Landroid/content/Context;I)I

    .line 447
    .line 448
    .line 449
    move-result v1

    .line 450
    :cond_11
    iget-boolean v7, v0, Lj1/f$d;->Q:Z

    .line 451
    .line 452
    if-nez v7, :cond_12

    .line 453
    .line 454
    iget-object v7, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 455
    .line 456
    sget v8, Lj1/g;->s:I

    .line 457
    .line 458
    invoke-static {v7, v8}, Ll1/a;->j(Landroid/content/Context;I)Z

    .line 459
    .line 460
    .line 461
    move-result v7

    .line 462
    if-eqz v7, :cond_13

    .line 463
    .line 464
    :cond_12
    iget-object v1, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 465
    .line 466
    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 467
    .line 468
    .line 469
    move-result-object v1

    .line 470
    sget v7, Lj1/i;->l:I

    .line 471
    .line 472
    invoke-virtual {v1, v7}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 473
    .line 474
    .line 475
    move-result v1

    .line 476
    :cond_13
    if-le v1, v6, :cond_14

    .line 477
    .line 478
    iget-object v7, p0, Lj1/f;->e:Landroid/widget/ImageView;

    .line 479
    .line 480
    invoke-virtual {v7, v3}, Landroid/widget/ImageView;->setAdjustViewBounds(Z)V

    .line 481
    .line 482
    .line 483
    iget-object v7, p0, Lj1/f;->e:Landroid/widget/ImageView;

    .line 484
    .line 485
    invoke-virtual {v7, v1}, Landroid/widget/ImageView;->setMaxHeight(I)V

    .line 486
    .line 487
    .line 488
    iget-object v7, p0, Lj1/f;->e:Landroid/widget/ImageView;

    .line 489
    .line 490
    invoke-virtual {v7, v1}, Landroid/widget/ImageView;->setMaxWidth(I)V

    .line 491
    .line 492
    .line 493
    iget-object v1, p0, Lj1/f;->e:Landroid/widget/ImageView;

    .line 494
    .line 495
    invoke-virtual {v1}, Landroid/view/View;->requestLayout()V

    .line 496
    .line 497
    .line 498
    :cond_14
    iget-boolean v1, v0, Lj1/f$d;->D0:Z

    .line 499
    .line 500
    if-nez v1, :cond_15

    .line 501
    .line 502
    invoke-virtual {p0}, Landroid/app/Dialog;->getContext()Landroid/content/Context;

    .line 503
    .line 504
    .line 505
    move-result-object v1

    .line 506
    sget v7, Lj1/g;->p:I

    .line 507
    .line 508
    invoke-static {v1, v7}, Ll1/a;->l(Landroid/content/Context;I)I

    .line 509
    .line 510
    .line 511
    move-result v1

    .line 512
    iget-object v7, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 513
    .line 514
    sget v8, Lj1/g;->q:I

    .line 515
    .line 516
    invoke-static {v7, v8, v1}, Ll1/a;->m(Landroid/content/Context;II)I

    .line 517
    .line 518
    .line 519
    move-result v1

    .line 520
    iput v1, v0, Lj1/f$d;->a0:I

    .line 521
    .line 522
    :cond_15
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 523
    .line 524
    iget v7, v0, Lj1/f$d;->a0:I

    .line 525
    .line 526
    invoke-virtual {v1, v7}, Lcom/afollestad/materialdialogs/internal/MDRootLayout;->setDividerColor(I)V

    .line 527
    .line 528
    .line 529
    iget-object v1, p0, Lj1/f;->f:Landroid/widget/TextView;

    .line 530
    .line 531
    if-eqz v1, :cond_17

    .line 532
    .line 533
    iget-object v7, v0, Lj1/f$d;->O:Landroid/graphics/Typeface;

    .line 534
    .line 535
    invoke-virtual {p0, v1, v7}, Lj1/f;->p(Landroid/widget/TextView;Landroid/graphics/Typeface;)V

    .line 536
    .line 537
    .line 538
    iget-object v1, p0, Lj1/f;->f:Landroid/widget/TextView;

    .line 539
    .line 540
    iget v7, v0, Lj1/f$d;->i:I

    .line 541
    .line 542
    invoke-virtual {v1, v7}, Landroid/widget/TextView;->setTextColor(I)V

    .line 543
    .line 544
    .line 545
    iget-object v1, p0, Lj1/f;->f:Landroid/widget/TextView;

    .line 546
    .line 547
    iget-object v7, v0, Lj1/f$d;->c:Lj1/e;

    .line 548
    .line 549
    invoke-virtual {v7}, Lj1/e;->a()I

    .line 550
    .line 551
    .line 552
    move-result v7

    .line 553
    invoke-virtual {v1, v7}, Landroid/widget/TextView;->setGravity(I)V

    .line 554
    .line 555
    .line 556
    iget-object v1, p0, Lj1/f;->f:Landroid/widget/TextView;

    .line 557
    .line 558
    iget-object v7, v0, Lj1/f$d;->c:Lj1/e;

    .line 559
    .line 560
    invoke-virtual {v7}, Lj1/e;->b()I

    .line 561
    .line 562
    .line 563
    move-result v7

    .line 564
    invoke-virtual {v1, v7}, Landroid/view/View;->setTextAlignment(I)V

    .line 565
    .line 566
    .line 567
    iget-object v1, v0, Lj1/f$d;->b:Ljava/lang/CharSequence;

    .line 568
    .line 569
    if-nez v1, :cond_16

    .line 570
    .line 571
    iget-object v1, p0, Lj1/f;->j:Landroid/view/View;

    .line 572
    .line 573
    invoke-virtual {v1, v4}, Landroid/view/View;->setVisibility(I)V

    .line 574
    .line 575
    .line 576
    goto :goto_4

    .line 577
    :cond_16
    iget-object v7, p0, Lj1/f;->f:Landroid/widget/TextView;

    .line 578
    .line 579
    invoke-virtual {v7, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 580
    .line 581
    .line 582
    iget-object v1, p0, Lj1/f;->j:Landroid/view/View;

    .line 583
    .line 584
    invoke-virtual {v1, v5}, Landroid/view/View;->setVisibility(I)V

    .line 585
    .line 586
    .line 587
    :cond_17
    :goto_4
    iget-object v1, p0, Lj1/f;->g:Landroid/widget/TextView;

    .line 588
    .line 589
    if-eqz v1, :cond_1a

    .line 590
    .line 591
    new-instance v7, Landroid/text/method/LinkMovementMethod;

    .line 592
    .line 593
    invoke-direct {v7}, Landroid/text/method/LinkMovementMethod;-><init>()V

    .line 594
    .line 595
    .line 596
    invoke-virtual {v1, v7}, Landroid/widget/TextView;->setMovementMethod(Landroid/text/method/MovementMethod;)V

    .line 597
    .line 598
    .line 599
    iget-object v1, p0, Lj1/f;->g:Landroid/widget/TextView;

    .line 600
    .line 601
    iget-object v7, v0, Lj1/f$d;->N:Landroid/graphics/Typeface;

    .line 602
    .line 603
    invoke-virtual {p0, v1, v7}, Lj1/f;->p(Landroid/widget/TextView;Landroid/graphics/Typeface;)V

    .line 604
    .line 605
    .line 606
    iget-object v1, p0, Lj1/f;->g:Landroid/widget/TextView;

    .line 607
    .line 608
    const/4 v7, 0x0

    .line 609
    iget v8, v0, Lj1/f$d;->I:F

    .line 610
    .line 611
    invoke-virtual {v1, v7, v8}, Landroid/widget/TextView;->setLineSpacing(FF)V

    .line 612
    .line 613
    .line 614
    iget-object v1, v0, Lj1/f$d;->y:Landroid/content/res/ColorStateList;

    .line 615
    .line 616
    if-nez v1, :cond_18

    .line 617
    .line 618
    iget-object v1, p0, Lj1/f;->g:Landroid/widget/TextView;

    .line 619
    .line 620
    invoke-virtual {p0}, Landroid/app/Dialog;->getContext()Landroid/content/Context;

    .line 621
    .line 622
    .line 623
    move-result-object v7

    .line 624
    invoke-static {v7, v2}, Ll1/a;->l(Landroid/content/Context;I)I

    .line 625
    .line 626
    .line 627
    move-result v2

    .line 628
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setLinkTextColor(I)V

    .line 629
    .line 630
    .line 631
    goto :goto_5

    .line 632
    :cond_18
    iget-object v2, p0, Lj1/f;->g:Landroid/widget/TextView;

    .line 633
    .line 634
    invoke-virtual {v2, v1}, Landroid/widget/TextView;->setLinkTextColor(Landroid/content/res/ColorStateList;)V

    .line 635
    .line 636
    .line 637
    :goto_5
    iget-object v1, p0, Lj1/f;->g:Landroid/widget/TextView;

    .line 638
    .line 639
    iget v2, v0, Lj1/f$d;->j:I

    .line 640
    .line 641
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setTextColor(I)V

    .line 642
    .line 643
    .line 644
    iget-object v1, p0, Lj1/f;->g:Landroid/widget/TextView;

    .line 645
    .line 646
    iget-object v2, v0, Lj1/f$d;->d:Lj1/e;

    .line 647
    .line 648
    invoke-virtual {v2}, Lj1/e;->a()I

    .line 649
    .line 650
    .line 651
    move-result v2

    .line 652
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setGravity(I)V

    .line 653
    .line 654
    .line 655
    iget-object v1, p0, Lj1/f;->g:Landroid/widget/TextView;

    .line 656
    .line 657
    iget-object v2, v0, Lj1/f$d;->d:Lj1/e;

    .line 658
    .line 659
    invoke-virtual {v2}, Lj1/e;->b()I

    .line 660
    .line 661
    .line 662
    move-result v2

    .line 663
    invoke-virtual {v1, v2}, Landroid/view/View;->setTextAlignment(I)V

    .line 664
    .line 665
    .line 666
    iget-object v1, v0, Lj1/f$d;->k:Ljava/lang/CharSequence;

    .line 667
    .line 668
    if-eqz v1, :cond_19

    .line 669
    .line 670
    iget-object v2, p0, Lj1/f;->g:Landroid/widget/TextView;

    .line 671
    .line 672
    invoke-virtual {v2, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 673
    .line 674
    .line 675
    iget-object v1, p0, Lj1/f;->g:Landroid/widget/TextView;

    .line 676
    .line 677
    invoke-virtual {v1, v5}, Landroid/view/View;->setVisibility(I)V

    .line 678
    .line 679
    .line 680
    goto :goto_6

    .line 681
    :cond_19
    iget-object v1, p0, Lj1/f;->g:Landroid/widget/TextView;

    .line 682
    .line 683
    invoke-virtual {v1, v4}, Landroid/view/View;->setVisibility(I)V

    .line 684
    .line 685
    .line 686
    :cond_1a
    :goto_6
    iget-object v1, p0, Lj1/f;->u:Landroid/widget/CheckBox;

    .line 687
    .line 688
    if-eqz v1, :cond_1b

    .line 689
    .line 690
    iget-object v2, v0, Lj1/f$d;->q0:Ljava/lang/CharSequence;

    .line 691
    .line 692
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 693
    .line 694
    .line 695
    iget-object v1, p0, Lj1/f;->u:Landroid/widget/CheckBox;

    .line 696
    .line 697
    iget-boolean v2, v0, Lj1/f$d;->r0:Z

    .line 698
    .line 699
    invoke-virtual {v1, v2}, Landroid/widget/CompoundButton;->setChecked(Z)V

    .line 700
    .line 701
    .line 702
    iget-object v1, p0, Lj1/f;->u:Landroid/widget/CheckBox;

    .line 703
    .line 704
    iget-object v2, v0, Lj1/f$d;->s0:Landroid/widget/CompoundButton$OnCheckedChangeListener;

    .line 705
    .line 706
    invoke-virtual {v1, v2}, Landroid/widget/CompoundButton;->setOnCheckedChangeListener(Landroid/widget/CompoundButton$OnCheckedChangeListener;)V

    .line 707
    .line 708
    .line 709
    iget-object v1, p0, Lj1/f;->u:Landroid/widget/CheckBox;

    .line 710
    .line 711
    iget-object v2, v0, Lj1/f$d;->N:Landroid/graphics/Typeface;

    .line 712
    .line 713
    invoke-virtual {p0, v1, v2}, Lj1/f;->p(Landroid/widget/TextView;Landroid/graphics/Typeface;)V

    .line 714
    .line 715
    .line 716
    iget-object v1, p0, Lj1/f;->u:Landroid/widget/CheckBox;

    .line 717
    .line 718
    iget v2, v0, Lj1/f$d;->j:I

    .line 719
    .line 720
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setTextColor(I)V

    .line 721
    .line 722
    .line 723
    iget-object v1, p0, Lj1/f;->u:Landroid/widget/CheckBox;

    .line 724
    .line 725
    iget v2, v0, Lj1/f$d;->t:I

    .line 726
    .line 727
    invoke-static {v1, v2}, Lk1/b;->c(Landroid/widget/CheckBox;I)V

    .line 728
    .line 729
    .line 730
    :cond_1b
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 731
    .line 732
    iget-object v2, v0, Lj1/f$d;->g:Lj1/e;

    .line 733
    .line 734
    invoke-virtual {v1, v2}, Lcom/afollestad/materialdialogs/internal/MDRootLayout;->setButtonGravity(Lj1/e;)V

    .line 735
    .line 736
    .line 737
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 738
    .line 739
    iget-object v2, v0, Lj1/f$d;->e:Lj1/e;

    .line 740
    .line 741
    invoke-virtual {v1, v2}, Lcom/afollestad/materialdialogs/internal/MDRootLayout;->setButtonStackedGravity(Lj1/e;)V

    .line 742
    .line 743
    .line 744
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 745
    .line 746
    iget-object v2, v0, Lj1/f$d;->Y:Lj1/o;

    .line 747
    .line 748
    invoke-virtual {v1, v2}, Lcom/afollestad/materialdialogs/internal/MDRootLayout;->setStackingBehavior(Lj1/o;)V

    .line 749
    .line 750
    .line 751
    iget-object v1, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 752
    .line 753
    const v2, 0x101038c

    .line 754
    .line 755
    .line 756
    invoke-static {v1, v2, v3}, Ll1/a;->k(Landroid/content/Context;IZ)Z

    .line 757
    .line 758
    .line 759
    move-result v1

    .line 760
    if-eqz v1, :cond_1c

    .line 761
    .line 762
    iget-object v1, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 763
    .line 764
    sget v2, Lj1/g;->G:I

    .line 765
    .line 766
    invoke-static {v1, v2, v3}, Ll1/a;->k(Landroid/content/Context;IZ)Z

    .line 767
    .line 768
    .line 769
    move-result v1

    .line 770
    :cond_1c
    iget-object v2, p0, Lj1/f;->v:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 771
    .line 772
    iget-object v4, v0, Lj1/f$d;->O:Landroid/graphics/Typeface;

    .line 773
    .line 774
    invoke-virtual {p0, v2, v4}, Lj1/f;->p(Landroid/widget/TextView;Landroid/graphics/Typeface;)V

    .line 775
    .line 776
    .line 777
    invoke-virtual {v2, v1}, Lcom/afollestad/materialdialogs/internal/MDButton;->setAllCapsCompat(Z)V

    .line 778
    .line 779
    .line 780
    iget-object v4, v0, Lj1/f$d;->m:Ljava/lang/CharSequence;

    .line 781
    .line 782
    invoke-virtual {v2, v4}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 783
    .line 784
    .line 785
    iget-object v4, v0, Lj1/f$d;->v:Landroid/content/res/ColorStateList;

    .line 786
    .line 787
    invoke-virtual {v2, v4}, Landroid/widget/TextView;->setTextColor(Landroid/content/res/ColorStateList;)V

    .line 788
    .line 789
    .line 790
    iget-object v2, p0, Lj1/f;->v:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 791
    .line 792
    sget-object v4, Lj1/b;->a:Lj1/b;

    .line 793
    .line 794
    invoke-virtual {p0, v4, v3}, Lj1/f;->g(Lj1/b;Z)Landroid/graphics/drawable/Drawable;

    .line 795
    .line 796
    .line 797
    move-result-object v7

    .line 798
    invoke-virtual {v2, v7}, Lcom/afollestad/materialdialogs/internal/MDButton;->setStackedSelector(Landroid/graphics/drawable/Drawable;)V

    .line 799
    .line 800
    .line 801
    iget-object v2, p0, Lj1/f;->v:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 802
    .line 803
    invoke-virtual {p0, v4, v5}, Lj1/f;->g(Lj1/b;Z)Landroid/graphics/drawable/Drawable;

    .line 804
    .line 805
    .line 806
    move-result-object v7

    .line 807
    invoke-virtual {v2, v7}, Lcom/afollestad/materialdialogs/internal/MDButton;->setDefaultSelector(Landroid/graphics/drawable/Drawable;)V

    .line 808
    .line 809
    .line 810
    iget-object v2, p0, Lj1/f;->v:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 811
    .line 812
    invoke-virtual {v2, v4}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 813
    .line 814
    .line 815
    iget-object v2, p0, Lj1/f;->v:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 816
    .line 817
    invoke-virtual {v2, p0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 818
    .line 819
    .line 820
    iget-object v2, p0, Lj1/f;->x:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 821
    .line 822
    iget-object v4, v0, Lj1/f$d;->O:Landroid/graphics/Typeface;

    .line 823
    .line 824
    invoke-virtual {p0, v2, v4}, Lj1/f;->p(Landroid/widget/TextView;Landroid/graphics/Typeface;)V

    .line 825
    .line 826
    .line 827
    invoke-virtual {v2, v1}, Lcom/afollestad/materialdialogs/internal/MDButton;->setAllCapsCompat(Z)V

    .line 828
    .line 829
    .line 830
    iget-object v4, v0, Lj1/f$d;->o:Ljava/lang/CharSequence;

    .line 831
    .line 832
    invoke-virtual {v2, v4}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 833
    .line 834
    .line 835
    iget-object v4, v0, Lj1/f$d;->w:Landroid/content/res/ColorStateList;

    .line 836
    .line 837
    invoke-virtual {v2, v4}, Landroid/widget/TextView;->setTextColor(Landroid/content/res/ColorStateList;)V

    .line 838
    .line 839
    .line 840
    iget-object v2, p0, Lj1/f;->x:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 841
    .line 842
    sget-object v4, Lj1/b;->c:Lj1/b;

    .line 843
    .line 844
    invoke-virtual {p0, v4, v3}, Lj1/f;->g(Lj1/b;Z)Landroid/graphics/drawable/Drawable;

    .line 845
    .line 846
    .line 847
    move-result-object v7

    .line 848
    invoke-virtual {v2, v7}, Lcom/afollestad/materialdialogs/internal/MDButton;->setStackedSelector(Landroid/graphics/drawable/Drawable;)V

    .line 849
    .line 850
    .line 851
    iget-object v2, p0, Lj1/f;->x:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 852
    .line 853
    invoke-virtual {p0, v4, v5}, Lj1/f;->g(Lj1/b;Z)Landroid/graphics/drawable/Drawable;

    .line 854
    .line 855
    .line 856
    move-result-object v7

    .line 857
    invoke-virtual {v2, v7}, Lcom/afollestad/materialdialogs/internal/MDButton;->setDefaultSelector(Landroid/graphics/drawable/Drawable;)V

    .line 858
    .line 859
    .line 860
    iget-object v2, p0, Lj1/f;->x:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 861
    .line 862
    invoke-virtual {v2, v4}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 863
    .line 864
    .line 865
    iget-object v2, p0, Lj1/f;->x:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 866
    .line 867
    invoke-virtual {v2, p0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 868
    .line 869
    .line 870
    iget-object v2, p0, Lj1/f;->w:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 871
    .line 872
    iget-object v4, v0, Lj1/f$d;->O:Landroid/graphics/Typeface;

    .line 873
    .line 874
    invoke-virtual {p0, v2, v4}, Lj1/f;->p(Landroid/widget/TextView;Landroid/graphics/Typeface;)V

    .line 875
    .line 876
    .line 877
    invoke-virtual {v2, v1}, Lcom/afollestad/materialdialogs/internal/MDButton;->setAllCapsCompat(Z)V

    .line 878
    .line 879
    .line 880
    iget-object v1, v0, Lj1/f$d;->n:Ljava/lang/CharSequence;

    .line 881
    .line 882
    invoke-virtual {v2, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 883
    .line 884
    .line 885
    iget-object v1, v0, Lj1/f$d;->x:Landroid/content/res/ColorStateList;

    .line 886
    .line 887
    invoke-virtual {v2, v1}, Landroid/widget/TextView;->setTextColor(Landroid/content/res/ColorStateList;)V

    .line 888
    .line 889
    .line 890
    iget-object v1, p0, Lj1/f;->w:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 891
    .line 892
    sget-object v2, Lj1/b;->b:Lj1/b;

    .line 893
    .line 894
    invoke-virtual {p0, v2, v3}, Lj1/f;->g(Lj1/b;Z)Landroid/graphics/drawable/Drawable;

    .line 895
    .line 896
    .line 897
    move-result-object v3

    .line 898
    invoke-virtual {v1, v3}, Lcom/afollestad/materialdialogs/internal/MDButton;->setStackedSelector(Landroid/graphics/drawable/Drawable;)V

    .line 899
    .line 900
    .line 901
    iget-object v1, p0, Lj1/f;->w:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 902
    .line 903
    invoke-virtual {p0, v2, v5}, Lj1/f;->g(Lj1/b;Z)Landroid/graphics/drawable/Drawable;

    .line 904
    .line 905
    .line 906
    move-result-object v3

    .line 907
    invoke-virtual {v1, v3}, Lcom/afollestad/materialdialogs/internal/MDButton;->setDefaultSelector(Landroid/graphics/drawable/Drawable;)V

    .line 908
    .line 909
    .line 910
    iget-object v1, p0, Lj1/f;->w:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 911
    .line 912
    invoke-virtual {v1, v2}, Landroid/view/View;->setTag(Ljava/lang/Object;)V

    .line 913
    .line 914
    .line 915
    iget-object v1, p0, Lj1/f;->w:Lcom/afollestad/materialdialogs/internal/MDButton;

    .line 916
    .line 917
    invoke-virtual {v1, p0}, Landroid/view/View;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 918
    .line 919
    .line 920
    iget-object v1, p0, Lj1/f;->i:Landroidx/recyclerview/widget/RecyclerView;

    .line 921
    .line 922
    if-eqz v1, :cond_1e

    .line 923
    .line 924
    iget-object v1, v0, Lj1/f$d;->S:Landroidx/recyclerview/widget/RecyclerView$h;

    .line 925
    .line 926
    if-nez v1, :cond_1d

    .line 927
    .line 928
    sget-object v1, Lj1/f$f;->a:Lj1/f$f;

    .line 929
    .line 930
    iput-object v1, p0, Lj1/f;->y:Lj1/f$f;

    .line 931
    .line 932
    new-instance v2, Lj1/a;

    .line 933
    .line 934
    invoke-static {v1}, Lj1/f$f;->a(Lj1/f$f;)I

    .line 935
    .line 936
    .line 937
    move-result v1

    .line 938
    invoke-direct {v2, p0, v1}, Lj1/a;-><init>(Lj1/f;I)V

    .line 939
    .line 940
    .line 941
    iput-object v2, v0, Lj1/f$d;->S:Landroidx/recyclerview/widget/RecyclerView$h;

    .line 942
    .line 943
    goto :goto_7

    .line 944
    :cond_1d
    instance-of v2, v1, Lk1/a;

    .line 945
    .line 946
    if-eqz v2, :cond_1e

    .line 947
    .line 948
    check-cast v1, Lk1/a;

    .line 949
    .line 950
    invoke-interface {v1, p0}, Lk1/a;->a(Lj1/f;)V

    .line 951
    .line 952
    .line 953
    :cond_1e
    :goto_7
    invoke-static {p0}, Lj1/d;->f(Lj1/f;)V

    .line 954
    .line 955
    .line 956
    invoke-static {p0}, Lj1/d;->e(Lj1/f;)V

    .line 957
    .line 958
    .line 959
    iget-object v1, v0, Lj1/f$d;->s:Landroid/view/View;

    .line 960
    .line 961
    if-eqz v1, :cond_22

    .line 962
    .line 963
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 964
    .line 965
    sget v2, Lj1/k;->l:I

    .line 966
    .line 967
    invoke-virtual {v1, v2}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 968
    .line 969
    .line 970
    move-result-object v1

    .line 971
    check-cast v1, Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 972
    .line 973
    invoke-virtual {v1}, Lcom/afollestad/materialdialogs/internal/MDRootLayout;->t()V

    .line 974
    .line 975
    .line 976
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 977
    .line 978
    sget v2, Lj1/k;->g:I

    .line 979
    .line 980
    invoke-virtual {v1, v2}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 981
    .line 982
    .line 983
    move-result-object v1

    .line 984
    check-cast v1, Landroid/widget/FrameLayout;

    .line 985
    .line 986
    iput-object v1, p0, Lj1/f;->k:Landroid/widget/FrameLayout;

    .line 987
    .line 988
    iget-object v2, v0, Lj1/f$d;->s:Landroid/view/View;

    .line 989
    .line 990
    invoke-virtual {v2}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 991
    .line 992
    .line 993
    move-result-object v3

    .line 994
    if-eqz v3, :cond_1f

    .line 995
    .line 996
    invoke-virtual {v2}, Landroid/view/View;->getParent()Landroid/view/ViewParent;

    .line 997
    .line 998
    .line 999
    move-result-object v3

    .line 1000
    check-cast v3, Landroid/view/ViewGroup;

    .line 1001
    .line 1002
    invoke-virtual {v3, v2}, Landroid/view/ViewGroup;->removeView(Landroid/view/View;)V

    .line 1003
    .line 1004
    .line 1005
    :cond_1f
    iget-boolean v3, v0, Lj1/f$d;->Z:Z

    .line 1006
    .line 1007
    const/4 v4, -0x2

    .line 1008
    if-eqz v3, :cond_21

    .line 1009
    .line 1010
    invoke-virtual {p0}, Landroid/app/Dialog;->getContext()Landroid/content/Context;

    .line 1011
    .line 1012
    .line 1013
    move-result-object v3

    .line 1014
    invoke-virtual {v3}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 1015
    .line 1016
    .line 1017
    move-result-object v3

    .line 1018
    sget v7, Lj1/i;->g:I

    .line 1019
    .line 1020
    invoke-virtual {v3, v7}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 1021
    .line 1022
    .line 1023
    move-result v7

    .line 1024
    new-instance v8, Landroid/widget/ScrollView;

    .line 1025
    .line 1026
    invoke-virtual {p0}, Landroid/app/Dialog;->getContext()Landroid/content/Context;

    .line 1027
    .line 1028
    .line 1029
    move-result-object v9

    .line 1030
    invoke-direct {v8, v9}, Landroid/widget/ScrollView;-><init>(Landroid/content/Context;)V

    .line 1031
    .line 1032
    .line 1033
    sget v9, Lj1/i;->f:I

    .line 1034
    .line 1035
    invoke-virtual {v3, v9}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 1036
    .line 1037
    .line 1038
    move-result v9

    .line 1039
    sget v10, Lj1/i;->e:I

    .line 1040
    .line 1041
    invoke-virtual {v3, v10}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 1042
    .line 1043
    .line 1044
    move-result v3

    .line 1045
    invoke-virtual {v8, v5}, Landroid/view/ViewGroup;->setClipToPadding(Z)V

    .line 1046
    .line 1047
    .line 1048
    instance-of v10, v2, Landroid/widget/EditText;

    .line 1049
    .line 1050
    if-eqz v10, :cond_20

    .line 1051
    .line 1052
    invoke-virtual {v8, v7, v9, v7, v3}, Landroid/view/View;->setPadding(IIII)V

    .line 1053
    .line 1054
    .line 1055
    goto :goto_8

    .line 1056
    :cond_20
    invoke-virtual {v8, v5, v9, v5, v3}, Landroid/view/View;->setPadding(IIII)V

    .line 1057
    .line 1058
    .line 1059
    invoke-virtual {v2, v7, v5, v7, v5}, Landroid/view/View;->setPadding(IIII)V

    .line 1060
    .line 1061
    .line 1062
    :goto_8
    new-instance v3, Landroid/widget/FrameLayout$LayoutParams;

    .line 1063
    .line 1064
    invoke-direct {v3, v6, v4}, Landroid/widget/FrameLayout$LayoutParams;-><init>(II)V

    .line 1065
    .line 1066
    .line 1067
    invoke-virtual {v8, v2, v3}, Landroid/widget/ScrollView;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 1068
    .line 1069
    .line 1070
    move-object v2, v8

    .line 1071
    :cond_21
    new-instance v3, Landroid/view/ViewGroup$LayoutParams;

    .line 1072
    .line 1073
    invoke-direct {v3, v6, v4}, Landroid/view/ViewGroup$LayoutParams;-><init>(II)V

    .line 1074
    .line 1075
    .line 1076
    invoke-virtual {v1, v2, v3}, Landroid/view/ViewGroup;->addView(Landroid/view/View;Landroid/view/ViewGroup$LayoutParams;)V

    .line 1077
    .line 1078
    .line 1079
    :cond_22
    iget-object v1, v0, Lj1/f$d;->X:Landroid/content/DialogInterface$OnShowListener;

    .line 1080
    .line 1081
    if-eqz v1, :cond_23

    .line 1082
    .line 1083
    invoke-virtual {p0, v1}, Lj1/c;->setOnShowListener(Landroid/content/DialogInterface$OnShowListener;)V

    .line 1084
    .line 1085
    .line 1086
    :cond_23
    iget-object v1, v0, Lj1/f$d;->V:Landroid/content/DialogInterface$OnCancelListener;

    .line 1087
    .line 1088
    if-eqz v1, :cond_24

    .line 1089
    .line 1090
    invoke-virtual {p0, v1}, Landroid/app/Dialog;->setOnCancelListener(Landroid/content/DialogInterface$OnCancelListener;)V

    .line 1091
    .line 1092
    .line 1093
    :cond_24
    iget-object v1, v0, Lj1/f$d;->U:Landroid/content/DialogInterface$OnDismissListener;

    .line 1094
    .line 1095
    if-eqz v1, :cond_25

    .line 1096
    .line 1097
    invoke-virtual {p0, v1}, Landroid/app/Dialog;->setOnDismissListener(Landroid/content/DialogInterface$OnDismissListener;)V

    .line 1098
    .line 1099
    .line 1100
    :cond_25
    iget-object v1, v0, Lj1/f$d;->W:Landroid/content/DialogInterface$OnKeyListener;

    .line 1101
    .line 1102
    if-eqz v1, :cond_26

    .line 1103
    .line 1104
    invoke-virtual {p0, v1}, Landroid/app/Dialog;->setOnKeyListener(Landroid/content/DialogInterface$OnKeyListener;)V

    .line 1105
    .line 1106
    .line 1107
    :cond_26
    invoke-virtual {p0}, Lj1/c;->b()V

    .line 1108
    .line 1109
    .line 1110
    invoke-virtual {p0}, Lj1/f;->l()V

    .line 1111
    .line 1112
    .line 1113
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 1114
    .line 1115
    invoke-virtual {p0, v1}, Lj1/c;->c(Landroid/view/View;)V

    .line 1116
    .line 1117
    .line 1118
    invoke-virtual {p0}, Lj1/f;->d()V

    .line 1119
    .line 1120
    .line 1121
    invoke-virtual {p0}, Landroid/app/Dialog;->getWindow()Landroid/view/Window;

    .line 1122
    .line 1123
    .line 1124
    move-result-object v1

    .line 1125
    invoke-virtual {v1}, Landroid/view/Window;->getWindowManager()Landroid/view/WindowManager;

    .line 1126
    .line 1127
    .line 1128
    move-result-object v1

    .line 1129
    invoke-interface {v1}, Landroid/view/WindowManager;->getDefaultDisplay()Landroid/view/Display;

    .line 1130
    .line 1131
    .line 1132
    move-result-object v1

    .line 1133
    new-instance v2, Landroid/graphics/Point;

    .line 1134
    .line 1135
    invoke-direct {v2}, Landroid/graphics/Point;-><init>()V

    .line 1136
    .line 1137
    .line 1138
    invoke-virtual {v1, v2}, Landroid/view/Display;->getSize(Landroid/graphics/Point;)V

    .line 1139
    .line 1140
    .line 1141
    iget v1, v2, Landroid/graphics/Point;->x:I

    .line 1142
    .line 1143
    iget v2, v2, Landroid/graphics/Point;->y:I

    .line 1144
    .line 1145
    iget-object v3, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 1146
    .line 1147
    invoke-virtual {v3}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 1148
    .line 1149
    .line 1150
    move-result-object v3

    .line 1151
    sget v4, Lj1/i;->j:I

    .line 1152
    .line 1153
    invoke-virtual {v3, v4}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 1154
    .line 1155
    .line 1156
    move-result v3

    .line 1157
    iget-object v4, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 1158
    .line 1159
    invoke-virtual {v4}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 1160
    .line 1161
    .line 1162
    move-result-object v4

    .line 1163
    sget v5, Lj1/i;->h:I

    .line 1164
    .line 1165
    invoke-virtual {v4, v5}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 1166
    .line 1167
    .line 1168
    move-result v4

    .line 1169
    iget-object v0, v0, Lj1/f$d;->a:Landroid/content/Context;

    .line 1170
    .line 1171
    invoke-virtual {v0}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    .line 1172
    .line 1173
    .line 1174
    move-result-object v0

    .line 1175
    sget v5, Lj1/i;->i:I

    .line 1176
    .line 1177
    invoke-virtual {v0, v5}, Landroid/content/res/Resources;->getDimensionPixelSize(I)I

    .line 1178
    .line 1179
    .line 1180
    move-result v0

    .line 1181
    mul-int/lit8 v4, v4, 0x2

    .line 1182
    .line 1183
    sub-int/2addr v1, v4

    .line 1184
    iget-object v4, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 1185
    .line 1186
    mul-int/lit8 v3, v3, 0x2

    .line 1187
    .line 1188
    sub-int/2addr v2, v3

    .line 1189
    invoke-virtual {v4, v2}, Lcom/afollestad/materialdialogs/internal/MDRootLayout;->setMaxHeight(I)V

    .line 1190
    .line 1191
    .line 1192
    new-instance v2, Landroid/view/WindowManager$LayoutParams;

    .line 1193
    .line 1194
    invoke-direct {v2}, Landroid/view/WindowManager$LayoutParams;-><init>()V

    .line 1195
    .line 1196
    .line 1197
    invoke-virtual {p0}, Landroid/app/Dialog;->getWindow()Landroid/view/Window;

    .line 1198
    .line 1199
    .line 1200
    move-result-object v3

    .line 1201
    invoke-virtual {v3}, Landroid/view/Window;->getAttributes()Landroid/view/WindowManager$LayoutParams;

    .line 1202
    .line 1203
    .line 1204
    move-result-object v3

    .line 1205
    invoke-virtual {v2, v3}, Landroid/view/WindowManager$LayoutParams;->copyFrom(Landroid/view/WindowManager$LayoutParams;)I

    .line 1206
    .line 1207
    .line 1208
    invoke-static {v0, v1}, Ljava/lang/Math;->min(II)I

    .line 1209
    .line 1210
    .line 1211
    move-result v0

    .line 1212
    iput v0, v2, Landroid/view/WindowManager$LayoutParams;->width:I

    .line 1213
    .line 1214
    invoke-virtual {p0}, Landroid/app/Dialog;->getWindow()Landroid/view/Window;

    .line 1215
    .line 1216
    .line 1217
    move-result-object p0

    .line 1218
    invoke-virtual {p0, v2}, Landroid/view/Window;->setAttributes(Landroid/view/WindowManager$LayoutParams;)V

    .line 1219
    .line 1220
    .line 1221
    return-void
.end method

.method private static e(Lj1/f;)V
    .locals 4

    .line 1
    iget-object v0, p0, Lj1/f;->c:Lj1/f$d;

    .line 2
    .line 3
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 4
    .line 5
    const v2, 0x1020009

    .line 6
    .line 7
    .line 8
    invoke-virtual {v1, v2}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    check-cast v1, Landroid/widget/EditText;

    .line 13
    .line 14
    iput-object v1, p0, Lj1/f;->h:Landroid/widget/EditText;

    .line 15
    .line 16
    if-nez v1, :cond_0

    .line 17
    .line 18
    return-void

    .line 19
    :cond_0
    iget-object v2, v0, Lj1/f$d;->N:Landroid/graphics/Typeface;

    .line 20
    .line 21
    invoke-virtual {p0, v1, v2}, Lj1/f;->p(Landroid/widget/TextView;Landroid/graphics/Typeface;)V

    .line 22
    .line 23
    .line 24
    iget-object v1, v0, Lj1/f$d;->h0:Ljava/lang/CharSequence;

    .line 25
    .line 26
    if-eqz v1, :cond_1

    .line 27
    .line 28
    iget-object v2, p0, Lj1/f;->h:Landroid/widget/EditText;

    .line 29
    .line 30
    invoke-virtual {v2, v1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 31
    .line 32
    .line 33
    :cond_1
    invoke-virtual {p0}, Lj1/f;->o()V

    .line 34
    .line 35
    .line 36
    iget-object v1, p0, Lj1/f;->h:Landroid/widget/EditText;

    .line 37
    .line 38
    iget-object v2, v0, Lj1/f$d;->i0:Ljava/lang/CharSequence;

    .line 39
    .line 40
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setHint(Ljava/lang/CharSequence;)V

    .line 41
    .line 42
    .line 43
    iget-object v1, p0, Lj1/f;->h:Landroid/widget/EditText;

    .line 44
    .line 45
    invoke-virtual {v1}, Landroid/widget/TextView;->setSingleLine()V

    .line 46
    .line 47
    .line 48
    iget-object v1, p0, Lj1/f;->h:Landroid/widget/EditText;

    .line 49
    .line 50
    iget v2, v0, Lj1/f$d;->j:I

    .line 51
    .line 52
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setTextColor(I)V

    .line 53
    .line 54
    .line 55
    iget-object v1, p0, Lj1/f;->h:Landroid/widget/EditText;

    .line 56
    .line 57
    iget v2, v0, Lj1/f$d;->j:I

    .line 58
    .line 59
    const v3, 0x3e99999a    # 0.3f

    .line 60
    .line 61
    .line 62
    invoke-static {v2, v3}, Ll1/a;->a(IF)I

    .line 63
    .line 64
    .line 65
    move-result v2

    .line 66
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setHintTextColor(I)V

    .line 67
    .line 68
    .line 69
    iget-object v1, p0, Lj1/f;->h:Landroid/widget/EditText;

    .line 70
    .line 71
    iget-object v2, p0, Lj1/f;->c:Lj1/f$d;

    .line 72
    .line 73
    iget v2, v2, Lj1/f$d;->t:I

    .line 74
    .line 75
    invoke-static {v1, v2}, Lk1/b;->e(Landroid/widget/EditText;I)V

    .line 76
    .line 77
    .line 78
    iget v1, v0, Lj1/f$d;->k0:I

    .line 79
    .line 80
    const/4 v2, -0x1

    .line 81
    if-eq v1, v2, :cond_2

    .line 82
    .line 83
    iget-object v3, p0, Lj1/f;->h:Landroid/widget/EditText;

    .line 84
    .line 85
    invoke-virtual {v3, v1}, Landroid/widget/TextView;->setInputType(I)V

    .line 86
    .line 87
    .line 88
    iget v1, v0, Lj1/f$d;->k0:I

    .line 89
    .line 90
    const/16 v3, 0x90

    .line 91
    .line 92
    if-eq v1, v3, :cond_2

    .line 93
    .line 94
    const/16 v3, 0x80

    .line 95
    .line 96
    and-int/2addr v1, v3

    .line 97
    if-ne v1, v3, :cond_2

    .line 98
    .line 99
    iget-object v1, p0, Lj1/f;->h:Landroid/widget/EditText;

    .line 100
    .line 101
    invoke-static {}, Landroid/text/method/PasswordTransformationMethod;->getInstance()Landroid/text/method/PasswordTransformationMethod;

    .line 102
    .line 103
    .line 104
    move-result-object v3

    .line 105
    invoke-virtual {v1, v3}, Landroid/widget/TextView;->setTransformationMethod(Landroid/text/method/TransformationMethod;)V

    .line 106
    .line 107
    .line 108
    :cond_2
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 109
    .line 110
    sget v3, Lj1/k;->j:I

    .line 111
    .line 112
    invoke-virtual {v1, v3}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 113
    .line 114
    .line 115
    move-result-object v1

    .line 116
    check-cast v1, Landroid/widget/TextView;

    .line 117
    .line 118
    iput-object v1, p0, Lj1/f;->t:Landroid/widget/TextView;

    .line 119
    .line 120
    iget v3, v0, Lj1/f$d;->m0:I

    .line 121
    .line 122
    if-gtz v3, :cond_4

    .line 123
    .line 124
    iget v3, v0, Lj1/f$d;->n0:I

    .line 125
    .line 126
    if-le v3, v2, :cond_3

    .line 127
    .line 128
    goto :goto_0

    .line 129
    :cond_3
    const/16 v0, 0x8

    .line 130
    .line 131
    invoke-virtual {v1, v0}, Landroid/view/View;->setVisibility(I)V

    .line 132
    .line 133
    .line 134
    const/4 v0, 0x0

    .line 135
    iput-object v0, p0, Lj1/f;->t:Landroid/widget/TextView;

    .line 136
    .line 137
    goto :goto_1

    .line 138
    :cond_4
    :goto_0
    iget-object v1, p0, Lj1/f;->h:Landroid/widget/EditText;

    .line 139
    .line 140
    invoke-virtual {v1}, Landroid/widget/EditText;->getText()Landroid/text/Editable;

    .line 141
    .line 142
    .line 143
    move-result-object v1

    .line 144
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 145
    .line 146
    .line 147
    move-result-object v1

    .line 148
    invoke-virtual {v1}, Ljava/lang/String;->length()I

    .line 149
    .line 150
    .line 151
    move-result v1

    .line 152
    iget-boolean v0, v0, Lj1/f$d;->j0:Z

    .line 153
    .line 154
    xor-int/lit8 v0, v0, 0x1

    .line 155
    .line 156
    invoke-virtual {p0, v1, v0}, Lj1/f;->k(IZ)V

    .line 157
    .line 158
    .line 159
    :goto_1
    return-void
.end method

.method private static f(Lj1/f;)V
    .locals 7

    .line 1
    iget-object v0, p0, Lj1/f;->c:Lj1/f$d;

    .line 2
    .line 3
    iget-boolean v1, v0, Lj1/f$d;->d0:Z

    .line 4
    .line 5
    if-nez v1, :cond_0

    .line 6
    .line 7
    iget v1, v0, Lj1/f$d;->f0:I

    .line 8
    .line 9
    const/4 v2, -0x2

    .line 10
    if-le v1, v2, :cond_9

    .line 11
    .line 12
    :cond_0
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 13
    .line 14
    const v2, 0x102000d

    .line 15
    .line 16
    .line 17
    invoke-virtual {v1, v2}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    check-cast v1, Landroid/widget/ProgressBar;

    .line 22
    .line 23
    iput-object v1, p0, Lj1/f;->l:Landroid/widget/ProgressBar;

    .line 24
    .line 25
    if-nez v1, :cond_1

    .line 26
    .line 27
    return-void

    .line 28
    :cond_1
    iget-boolean v1, v0, Lj1/f$d;->d0:Z

    .line 29
    .line 30
    if-eqz v1, :cond_3

    .line 31
    .line 32
    iget-boolean v1, v0, Lj1/f$d;->v0:Z

    .line 33
    .line 34
    if-eqz v1, :cond_2

    .line 35
    .line 36
    new-instance v1, Lme/zhanghai/android/materialprogressbar/IndeterminateHorizontalProgressDrawable;

    .line 37
    .line 38
    invoke-virtual {v0}, Lj1/f$d;->e()Landroid/content/Context;

    .line 39
    .line 40
    .line 41
    move-result-object v2

    .line 42
    invoke-direct {v1, v2}, Lme/zhanghai/android/materialprogressbar/IndeterminateHorizontalProgressDrawable;-><init>(Landroid/content/Context;)V

    .line 43
    .line 44
    .line 45
    iget v2, v0, Lj1/f$d;->t:I

    .line 46
    .line 47
    invoke-virtual {v1, v2}, Lme/zhanghai/android/materialprogressbar/IndeterminateHorizontalProgressDrawable;->setTint(I)V

    .line 48
    .line 49
    .line 50
    iget-object v2, p0, Lj1/f;->l:Landroid/widget/ProgressBar;

    .line 51
    .line 52
    invoke-virtual {v2, v1}, Landroid/widget/ProgressBar;->setProgressDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 53
    .line 54
    .line 55
    iget-object v2, p0, Lj1/f;->l:Landroid/widget/ProgressBar;

    .line 56
    .line 57
    invoke-virtual {v2, v1}, Landroid/widget/ProgressBar;->setIndeterminateDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 58
    .line 59
    .line 60
    goto :goto_0

    .line 61
    :cond_2
    new-instance v1, Lme/zhanghai/android/materialprogressbar/IndeterminateCircularProgressDrawable;

    .line 62
    .line 63
    invoke-virtual {v0}, Lj1/f$d;->e()Landroid/content/Context;

    .line 64
    .line 65
    .line 66
    move-result-object v2

    .line 67
    invoke-direct {v1, v2}, Lme/zhanghai/android/materialprogressbar/IndeterminateCircularProgressDrawable;-><init>(Landroid/content/Context;)V

    .line 68
    .line 69
    .line 70
    iget v2, v0, Lj1/f$d;->t:I

    .line 71
    .line 72
    invoke-virtual {v1, v2}, Lme/zhanghai/android/materialprogressbar/IndeterminateCircularProgressDrawable;->setTint(I)V

    .line 73
    .line 74
    .line 75
    iget-object v2, p0, Lj1/f;->l:Landroid/widget/ProgressBar;

    .line 76
    .line 77
    invoke-virtual {v2, v1}, Landroid/widget/ProgressBar;->setProgressDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 78
    .line 79
    .line 80
    iget-object v2, p0, Lj1/f;->l:Landroid/widget/ProgressBar;

    .line 81
    .line 82
    invoke-virtual {v2, v1}, Landroid/widget/ProgressBar;->setIndeterminateDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 83
    .line 84
    .line 85
    goto :goto_0

    .line 86
    :cond_3
    new-instance v1, Lme/zhanghai/android/materialprogressbar/HorizontalProgressDrawable;

    .line 87
    .line 88
    invoke-virtual {v0}, Lj1/f$d;->e()Landroid/content/Context;

    .line 89
    .line 90
    .line 91
    move-result-object v2

    .line 92
    invoke-direct {v1, v2}, Lme/zhanghai/android/materialprogressbar/HorizontalProgressDrawable;-><init>(Landroid/content/Context;)V

    .line 93
    .line 94
    .line 95
    iget v2, v0, Lj1/f$d;->t:I

    .line 96
    .line 97
    invoke-virtual {v1, v2}, Lme/zhanghai/android/materialprogressbar/HorizontalProgressDrawable;->setTint(I)V

    .line 98
    .line 99
    .line 100
    iget-object v2, p0, Lj1/f;->l:Landroid/widget/ProgressBar;

    .line 101
    .line 102
    invoke-virtual {v2, v1}, Landroid/widget/ProgressBar;->setProgressDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 103
    .line 104
    .line 105
    iget-object v2, p0, Lj1/f;->l:Landroid/widget/ProgressBar;

    .line 106
    .line 107
    invoke-virtual {v2, v1}, Landroid/widget/ProgressBar;->setIndeterminateDrawable(Landroid/graphics/drawable/Drawable;)V

    .line 108
    .line 109
    .line 110
    :goto_0
    iget-boolean v1, v0, Lj1/f$d;->d0:Z

    .line 111
    .line 112
    if-eqz v1, :cond_4

    .line 113
    .line 114
    iget-boolean v2, v0, Lj1/f$d;->v0:Z

    .line 115
    .line 116
    if-eqz v2, :cond_9

    .line 117
    .line 118
    :cond_4
    iget-object v2, p0, Lj1/f;->l:Landroid/widget/ProgressBar;

    .line 119
    .line 120
    const/4 v3, 0x1

    .line 121
    const/4 v4, 0x0

    .line 122
    if-eqz v1, :cond_5

    .line 123
    .line 124
    iget-boolean v1, v0, Lj1/f$d;->v0:Z

    .line 125
    .line 126
    if-eqz v1, :cond_5

    .line 127
    .line 128
    const/4 v1, 0x1

    .line 129
    goto :goto_1

    .line 130
    :cond_5
    const/4 v1, 0x0

    .line 131
    :goto_1
    invoke-virtual {v2, v1}, Landroid/widget/ProgressBar;->setIndeterminate(Z)V

    .line 132
    .line 133
    .line 134
    iget-object v1, p0, Lj1/f;->l:Landroid/widget/ProgressBar;

    .line 135
    .line 136
    invoke-virtual {v1, v4}, Landroid/widget/ProgressBar;->setProgress(I)V

    .line 137
    .line 138
    .line 139
    iget-object v1, p0, Lj1/f;->l:Landroid/widget/ProgressBar;

    .line 140
    .line 141
    iget v2, v0, Lj1/f$d;->g0:I

    .line 142
    .line 143
    invoke-virtual {v1, v2}, Landroid/widget/ProgressBar;->setMax(I)V

    .line 144
    .line 145
    .line 146
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 147
    .line 148
    sget v2, Lj1/k;->i:I

    .line 149
    .line 150
    invoke-virtual {v1, v2}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 151
    .line 152
    .line 153
    move-result-object v1

    .line 154
    check-cast v1, Landroid/widget/TextView;

    .line 155
    .line 156
    iput-object v1, p0, Lj1/f;->r:Landroid/widget/TextView;

    .line 157
    .line 158
    if-eqz v1, :cond_6

    .line 159
    .line 160
    iget v2, v0, Lj1/f$d;->j:I

    .line 161
    .line 162
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setTextColor(I)V

    .line 163
    .line 164
    .line 165
    iget-object v1, p0, Lj1/f;->r:Landroid/widget/TextView;

    .line 166
    .line 167
    iget-object v2, v0, Lj1/f$d;->O:Landroid/graphics/Typeface;

    .line 168
    .line 169
    invoke-virtual {p0, v1, v2}, Lj1/f;->p(Landroid/widget/TextView;Landroid/graphics/Typeface;)V

    .line 170
    .line 171
    .line 172
    iget-object v1, p0, Lj1/f;->r:Landroid/widget/TextView;

    .line 173
    .line 174
    iget-object v2, v0, Lj1/f$d;->u0:Ljava/text/NumberFormat;

    .line 175
    .line 176
    const-wide/16 v5, 0x0

    .line 177
    .line 178
    invoke-virtual {v2, v5, v6}, Ljava/text/NumberFormat;->format(J)Ljava/lang/String;

    .line 179
    .line 180
    .line 181
    move-result-object v2

    .line 182
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 183
    .line 184
    .line 185
    :cond_6
    iget-object v1, p0, Lj1/c;->a:Lcom/afollestad/materialdialogs/internal/MDRootLayout;

    .line 186
    .line 187
    sget v2, Lj1/k;->j:I

    .line 188
    .line 189
    invoke-virtual {v1, v2}, Landroid/view/View;->findViewById(I)Landroid/view/View;

    .line 190
    .line 191
    .line 192
    move-result-object v1

    .line 193
    check-cast v1, Landroid/widget/TextView;

    .line 194
    .line 195
    iput-object v1, p0, Lj1/f;->s:Landroid/widget/TextView;

    .line 196
    .line 197
    if-eqz v1, :cond_8

    .line 198
    .line 199
    iget v2, v0, Lj1/f$d;->j:I

    .line 200
    .line 201
    invoke-virtual {v1, v2}, Landroid/widget/TextView;->setTextColor(I)V

    .line 202
    .line 203
    .line 204
    iget-object v1, p0, Lj1/f;->s:Landroid/widget/TextView;

    .line 205
    .line 206
    iget-object v2, v0, Lj1/f$d;->N:Landroid/graphics/Typeface;

    .line 207
    .line 208
    invoke-virtual {p0, v1, v2}, Lj1/f;->p(Landroid/widget/TextView;Landroid/graphics/Typeface;)V

    .line 209
    .line 210
    .line 211
    iget-boolean v1, v0, Lj1/f$d;->e0:Z

    .line 212
    .line 213
    if-eqz v1, :cond_7

    .line 214
    .line 215
    iget-object v1, p0, Lj1/f;->s:Landroid/widget/TextView;

    .line 216
    .line 217
    invoke-virtual {v1, v4}, Landroid/view/View;->setVisibility(I)V

    .line 218
    .line 219
    .line 220
    iget-object v1, p0, Lj1/f;->s:Landroid/widget/TextView;

    .line 221
    .line 222
    iget-object v2, v0, Lj1/f$d;->t0:Ljava/lang/String;

    .line 223
    .line 224
    const/4 v5, 0x2

    .line 225
    new-array v5, v5, [Ljava/lang/Object;

    .line 226
    .line 227
    invoke-static {v4}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 228
    .line 229
    .line 230
    move-result-object v6

    .line 231
    aput-object v6, v5, v4

    .line 232
    .line 233
    iget v0, v0, Lj1/f$d;->g0:I

    .line 234
    .line 235
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 236
    .line 237
    .line 238
    move-result-object v0

    .line 239
    aput-object v0, v5, v3

    .line 240
    .line 241
    invoke-static {v2, v5}, Ljava/lang/String;->format(Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/String;

    .line 242
    .line 243
    .line 244
    move-result-object v0

    .line 245
    invoke-virtual {v1, v0}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 246
    .line 247
    .line 248
    iget-object v0, p0, Lj1/f;->l:Landroid/widget/ProgressBar;

    .line 249
    .line 250
    invoke-virtual {v0}, Landroid/view/View;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    .line 251
    .line 252
    .line 253
    move-result-object v0

    .line 254
    check-cast v0, Landroid/view/ViewGroup$MarginLayoutParams;

    .line 255
    .line 256
    iput v4, v0, Landroid/view/ViewGroup$MarginLayoutParams;->leftMargin:I

    .line 257
    .line 258
    iput v4, v0, Landroid/view/ViewGroup$MarginLayoutParams;->rightMargin:I

    .line 259
    .line 260
    goto :goto_2

    .line 261
    :cond_7
    iget-object v0, p0, Lj1/f;->s:Landroid/widget/TextView;

    .line 262
    .line 263
    const/16 v1, 0x8

    .line 264
    .line 265
    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    .line 266
    .line 267
    .line 268
    goto :goto_2

    .line 269
    :cond_8
    iput-boolean v4, v0, Lj1/f$d;->e0:Z

    .line 270
    .line 271
    :cond_9
    :goto_2
    iget-object p0, p0, Lj1/f;->l:Landroid/widget/ProgressBar;

    .line 272
    .line 273
    if-eqz p0, :cond_a

    .line 274
    .line 275
    invoke-static {p0}, Lj1/d;->a(Landroid/widget/ProgressBar;)V

    .line 276
    .line 277
    .line 278
    :cond_a
    return-void
.end method
