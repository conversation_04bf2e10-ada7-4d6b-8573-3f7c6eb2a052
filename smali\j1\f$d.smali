.class public Lj1/f$d;
.super Ljava/lang/Object;
.source "MaterialDialog.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lj1/f;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "d"
.end annotation


# instance fields
.field protected A:Lj1/f$g;

.field protected A0:Z

.field protected B:Lj1/f$g;

.field protected B0:Z

.field protected C:Lj1/f$g;

.field protected C0:Z

.field protected D:Z

.field protected D0:Z

.field protected E:Z

.field protected E0:I

.field protected F:Lj1/p;

.field protected F0:I

.field protected G:Z

.field protected G0:I

.field protected H:Z

.field protected H0:I

.field protected I:F

.field protected I0:I

.field protected J:I

.field protected K:[Ljava/lang/Integer;

.field protected L:[Ljava/lang/Integer;

.field protected M:Z

.field protected N:Landroid/graphics/Typeface;

.field protected O:Landroid/graphics/Typeface;

.field protected P:Landroid/graphics/drawable/Drawable;

.field protected Q:Z

.field protected R:I

.field protected S:Landroidx/recyclerview/widget/RecyclerView$h;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Landroidx/recyclerview/widget/RecyclerView$h<",
            "*>;"
        }
    .end annotation
.end field

.field protected T:Landroidx/recyclerview/widget/RecyclerView$p;

.field protected U:Landroid/content/DialogInterface$OnDismissListener;

.field protected V:Landroid/content/DialogInterface$OnCancelListener;

.field protected W:Landroid/content/DialogInterface$OnKeyListener;

.field protected X:Landroid/content/DialogInterface$OnShowListener;

.field protected Y:Lj1/o;

.field protected Z:Z

.field protected final a:Landroid/content/Context;

.field protected a0:I

.field protected b:Ljava/lang/CharSequence;

.field protected b0:I

.field protected c:Lj1/e;

.field protected c0:I

.field protected d:Lj1/e;

.field protected d0:Z

.field protected e:Lj1/e;

.field protected e0:Z

.field protected f:Lj1/e;

.field protected f0:I

.field protected g:Lj1/e;

.field protected g0:I

.field protected h:I

.field protected h0:Ljava/lang/CharSequence;

.field protected i:I

.field protected i0:Ljava/lang/CharSequence;

.field protected j:I

.field protected j0:Z

.field protected k:Ljava/lang/CharSequence;

.field protected k0:I

.field protected l:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Ljava/lang/CharSequence;",
            ">;"
        }
    .end annotation
.end field

.field protected l0:Z

.field protected m:Ljava/lang/CharSequence;

.field protected m0:I

.field protected n:Ljava/lang/CharSequence;

.field protected n0:I

.field protected o:Ljava/lang/CharSequence;

.field protected o0:I

.field protected p:Z

.field protected p0:[I

.field protected q:Z

.field protected q0:Ljava/lang/CharSequence;

.field protected r:Z

.field protected r0:Z

.field protected s:Landroid/view/View;

.field protected s0:Landroid/widget/CompoundButton$OnCheckedChangeListener;

.field protected t:I

.field protected t0:Ljava/lang/String;

.field protected u:Landroid/content/res/ColorStateList;

.field protected u0:Ljava/text/NumberFormat;

.field protected v:Landroid/content/res/ColorStateList;

.field protected v0:Z

.field protected w:Landroid/content/res/ColorStateList;

.field protected w0:Z

.field protected x:Landroid/content/res/ColorStateList;

.field protected x0:Z

.field protected y:Landroid/content/res/ColorStateList;

.field protected y0:Z

.field protected z:Lj1/f$g;

.field protected z0:Z


# direct methods
.method public constructor <init>(Landroid/content/Context;)V
    .locals 5

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    sget-object v0, Lj1/e;->a:Lj1/e;

    .line 5
    .line 6
    iput-object v0, p0, Lj1/f$d;->c:Lj1/e;

    .line 7
    .line 8
    iput-object v0, p0, Lj1/f$d;->d:Lj1/e;

    .line 9
    .line 10
    sget-object v1, Lj1/e;->c:Lj1/e;

    .line 11
    .line 12
    iput-object v1, p0, Lj1/f$d;->e:Lj1/e;

    .line 13
    .line 14
    iput-object v0, p0, Lj1/f$d;->f:Lj1/e;

    .line 15
    .line 16
    iput-object v0, p0, Lj1/f$d;->g:Lj1/e;

    .line 17
    .line 18
    const/4 v0, 0x0

    .line 19
    iput v0, p0, Lj1/f$d;->h:I

    .line 20
    .line 21
    const/4 v1, -0x1

    .line 22
    iput v1, p0, Lj1/f$d;->i:I

    .line 23
    .line 24
    iput v1, p0, Lj1/f$d;->j:I

    .line 25
    .line 26
    iput-boolean v0, p0, Lj1/f$d;->D:Z

    .line 27
    .line 28
    iput-boolean v0, p0, Lj1/f$d;->E:Z

    .line 29
    .line 30
    sget-object v2, Lj1/p;->a:Lj1/p;

    .line 31
    .line 32
    iput-object v2, p0, Lj1/f$d;->F:Lj1/p;

    .line 33
    .line 34
    const/4 v3, 0x1

    .line 35
    iput-boolean v3, p0, Lj1/f$d;->G:Z

    .line 36
    .line 37
    iput-boolean v3, p0, Lj1/f$d;->H:Z

    .line 38
    .line 39
    const v4, 0x3f99999a    # 1.2f

    .line 40
    .line 41
    .line 42
    iput v4, p0, Lj1/f$d;->I:F

    .line 43
    .line 44
    iput v1, p0, Lj1/f$d;->J:I

    .line 45
    .line 46
    const/4 v4, 0x0

    .line 47
    iput-object v4, p0, Lj1/f$d;->K:[Ljava/lang/Integer;

    .line 48
    .line 49
    iput-object v4, p0, Lj1/f$d;->L:[Ljava/lang/Integer;

    .line 50
    .line 51
    iput-boolean v3, p0, Lj1/f$d;->M:Z

    .line 52
    .line 53
    iput v1, p0, Lj1/f$d;->R:I

    .line 54
    .line 55
    const/4 v3, -0x2

    .line 56
    iput v3, p0, Lj1/f$d;->f0:I

    .line 57
    .line 58
    iput v0, p0, Lj1/f$d;->g0:I

    .line 59
    .line 60
    iput v1, p0, Lj1/f$d;->k0:I

    .line 61
    .line 62
    iput v1, p0, Lj1/f$d;->m0:I

    .line 63
    .line 64
    iput v1, p0, Lj1/f$d;->n0:I

    .line 65
    .line 66
    iput v0, p0, Lj1/f$d;->o0:I

    .line 67
    .line 68
    iput-boolean v0, p0, Lj1/f$d;->w0:Z

    .line 69
    .line 70
    iput-boolean v0, p0, Lj1/f$d;->x0:Z

    .line 71
    .line 72
    iput-boolean v0, p0, Lj1/f$d;->y0:Z

    .line 73
    .line 74
    iput-boolean v0, p0, Lj1/f$d;->z0:Z

    .line 75
    .line 76
    iput-boolean v0, p0, Lj1/f$d;->A0:Z

    .line 77
    .line 78
    iput-boolean v0, p0, Lj1/f$d;->B0:Z

    .line 79
    .line 80
    iput-boolean v0, p0, Lj1/f$d;->C0:Z

    .line 81
    .line 82
    iput-boolean v0, p0, Lj1/f$d;->D0:Z

    .line 83
    .line 84
    iput-object p1, p0, Lj1/f$d;->a:Landroid/content/Context;

    .line 85
    .line 86
    sget v1, Lj1/h;->a:I

    .line 87
    .line 88
    invoke-static {p1, v1}, Ll1/a;->c(Landroid/content/Context;I)I

    .line 89
    .line 90
    .line 91
    move-result v1

    .line 92
    sget v3, Lj1/g;->a:I

    .line 93
    .line 94
    invoke-static {p1, v3, v1}, Ll1/a;->m(Landroid/content/Context;II)I

    .line 95
    .line 96
    .line 97
    move-result v1

    .line 98
    iput v1, p0, Lj1/f$d;->t:I

    .line 99
    .line 100
    const v3, 0x1010435

    .line 101
    .line 102
    .line 103
    invoke-static {p1, v3, v1}, Ll1/a;->m(Landroid/content/Context;II)I

    .line 104
    .line 105
    .line 106
    move-result v1

    .line 107
    iput v1, p0, Lj1/f$d;->t:I

    .line 108
    .line 109
    invoke-static {p1, v1}, Ll1/a;->b(Landroid/content/Context;I)Landroid/content/res/ColorStateList;

    .line 110
    .line 111
    .line 112
    move-result-object v1

    .line 113
    iput-object v1, p0, Lj1/f$d;->v:Landroid/content/res/ColorStateList;

    .line 114
    .line 115
    iget v1, p0, Lj1/f$d;->t:I

    .line 116
    .line 117
    invoke-static {p1, v1}, Ll1/a;->b(Landroid/content/Context;I)Landroid/content/res/ColorStateList;

    .line 118
    .line 119
    .line 120
    move-result-object v1

    .line 121
    iput-object v1, p0, Lj1/f$d;->w:Landroid/content/res/ColorStateList;

    .line 122
    .line 123
    iget v1, p0, Lj1/f$d;->t:I

    .line 124
    .line 125
    invoke-static {p1, v1}, Ll1/a;->b(Landroid/content/Context;I)Landroid/content/res/ColorStateList;

    .line 126
    .line 127
    .line 128
    move-result-object v1

    .line 129
    iput-object v1, p0, Lj1/f$d;->x:Landroid/content/res/ColorStateList;

    .line 130
    .line 131
    sget v1, Lj1/g;->w:I

    .line 132
    .line 133
    iget v3, p0, Lj1/f$d;->t:I

    .line 134
    .line 135
    invoke-static {p1, v1, v3}, Ll1/a;->m(Landroid/content/Context;II)I

    .line 136
    .line 137
    .line 138
    move-result v1

    .line 139
    invoke-static {p1, v1}, Ll1/a;->b(Landroid/content/Context;I)Landroid/content/res/ColorStateList;

    .line 140
    .line 141
    .line 142
    move-result-object v1

    .line 143
    iput-object v1, p0, Lj1/f$d;->y:Landroid/content/res/ColorStateList;

    .line 144
    .line 145
    const v1, 0x101042c

    .line 146
    .line 147
    .line 148
    invoke-static {p1, v1}, Ll1/a;->l(Landroid/content/Context;I)I

    .line 149
    .line 150
    .line 151
    move-result v1

    .line 152
    sget v3, Lj1/g;->i:I

    .line 153
    .line 154
    sget v4, Lj1/g;->c:I

    .line 155
    .line 156
    invoke-static {p1, v4, v1}, Ll1/a;->m(Landroid/content/Context;II)I

    .line 157
    .line 158
    .line 159
    move-result v1

    .line 160
    invoke-static {p1, v3, v1}, Ll1/a;->m(Landroid/content/Context;II)I

    .line 161
    .line 162
    .line 163
    move-result v1

    .line 164
    iput v1, p0, Lj1/f$d;->h:I

    .line 165
    .line 166
    invoke-static {}, Ljava/text/NumberFormat;->getPercentInstance()Ljava/text/NumberFormat;

    .line 167
    .line 168
    .line 169
    move-result-object v1

    .line 170
    iput-object v1, p0, Lj1/f$d;->u0:Ljava/text/NumberFormat;

    .line 171
    .line 172
    const-string v1, "%1d/%2d"

    .line 173
    .line 174
    iput-object v1, p0, Lj1/f$d;->t0:Ljava/lang/String;

    .line 175
    .line 176
    const v1, 0x1010036

    .line 177
    .line 178
    .line 179
    invoke-static {p1, v1}, Ll1/a;->l(Landroid/content/Context;I)I

    .line 180
    .line 181
    .line 182
    move-result v1

    .line 183
    invoke-static {v1}, Ll1/a;->g(I)Z

    .line 184
    .line 185
    .line 186
    move-result v1

    .line 187
    if-eqz v1, :cond_0

    .line 188
    .line 189
    goto :goto_0

    .line 190
    :cond_0
    sget-object v2, Lj1/p;->b:Lj1/p;

    .line 191
    .line 192
    :goto_0
    iput-object v2, p0, Lj1/f$d;->F:Lj1/p;

    .line 193
    .line 194
    invoke-direct {p0}, Lj1/f$d;->c()V

    .line 195
    .line 196
    .line 197
    sget v1, Lj1/g;->E:I

    .line 198
    .line 199
    iget-object v2, p0, Lj1/f$d;->c:Lj1/e;

    .line 200
    .line 201
    invoke-static {p1, v1, v2}, Ll1/a;->r(Landroid/content/Context;ILj1/e;)Lj1/e;

    .line 202
    .line 203
    .line 204
    move-result-object v1

    .line 205
    iput-object v1, p0, Lj1/f$d;->c:Lj1/e;

    .line 206
    .line 207
    sget v1, Lj1/g;->n:I

    .line 208
    .line 209
    iget-object v2, p0, Lj1/f$d;->d:Lj1/e;

    .line 210
    .line 211
    invoke-static {p1, v1, v2}, Ll1/a;->r(Landroid/content/Context;ILj1/e;)Lj1/e;

    .line 212
    .line 213
    .line 214
    move-result-object v1

    .line 215
    iput-object v1, p0, Lj1/f$d;->d:Lj1/e;

    .line 216
    .line 217
    sget v1, Lj1/g;->k:I

    .line 218
    .line 219
    iget-object v2, p0, Lj1/f$d;->e:Lj1/e;

    .line 220
    .line 221
    invoke-static {p1, v1, v2}, Ll1/a;->r(Landroid/content/Context;ILj1/e;)Lj1/e;

    .line 222
    .line 223
    .line 224
    move-result-object v1

    .line 225
    iput-object v1, p0, Lj1/f$d;->e:Lj1/e;

    .line 226
    .line 227
    sget v1, Lj1/g;->v:I

    .line 228
    .line 229
    iget-object v2, p0, Lj1/f$d;->f:Lj1/e;

    .line 230
    .line 231
    invoke-static {p1, v1, v2}, Ll1/a;->r(Landroid/content/Context;ILj1/e;)Lj1/e;

    .line 232
    .line 233
    .line 234
    move-result-object v1

    .line 235
    iput-object v1, p0, Lj1/f$d;->f:Lj1/e;

    .line 236
    .line 237
    sget v1, Lj1/g;->l:I

    .line 238
    .line 239
    iget-object v2, p0, Lj1/f$d;->g:Lj1/e;

    .line 240
    .line 241
    invoke-static {p1, v1, v2}, Ll1/a;->r(Landroid/content/Context;ILj1/e;)Lj1/e;

    .line 242
    .line 243
    .line 244
    move-result-object v1

    .line 245
    iput-object v1, p0, Lj1/f$d;->g:Lj1/e;

    .line 246
    .line 247
    sget v1, Lj1/g;->y:I

    .line 248
    .line 249
    invoke-static {p1, v1}, Ll1/a;->s(Landroid/content/Context;I)Ljava/lang/String;

    .line 250
    .line 251
    .line 252
    move-result-object v1

    .line 253
    sget v2, Lj1/g;->C:I

    .line 254
    .line 255
    invoke-static {p1, v2}, Ll1/a;->s(Landroid/content/Context;I)Ljava/lang/String;

    .line 256
    .line 257
    .line 258
    move-result-object p1

    .line 259
    :try_start_0
    invoke-virtual {p0, v1, p1}, Lj1/f$d;->n(Ljava/lang/String;Ljava/lang/String;)Lj1/f$d;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 260
    .line 261
    .line 262
    goto :goto_1

    .line 263
    :catchall_0
    nop

    .line 264
    :goto_1
    iget-object p1, p0, Lj1/f$d;->O:Landroid/graphics/Typeface;

    .line 265
    .line 266
    if-nez p1, :cond_1

    .line 267
    .line 268
    :try_start_1
    const-string p1, "sans-serif-medium"

    .line 269
    .line 270
    invoke-static {p1, v0}, Landroid/graphics/Typeface;->create(Ljava/lang/String;I)Landroid/graphics/Typeface;

    .line 271
    .line 272
    .line 273
    move-result-object p1

    .line 274
    iput-object p1, p0, Lj1/f$d;->O:Landroid/graphics/Typeface;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_1

    .line 275
    .line 276
    goto :goto_2

    .line 277
    :catchall_1
    sget-object p1, Landroid/graphics/Typeface;->DEFAULT_BOLD:Landroid/graphics/Typeface;

    .line 278
    .line 279
    iput-object p1, p0, Lj1/f$d;->O:Landroid/graphics/Typeface;

    .line 280
    .line 281
    :cond_1
    :goto_2
    iget-object p1, p0, Lj1/f$d;->N:Landroid/graphics/Typeface;

    .line 282
    .line 283
    if-nez p1, :cond_2

    .line 284
    .line 285
    :try_start_2
    const-string p1, "sans-serif"

    .line 286
    .line 287
    invoke-static {p1, v0}, Landroid/graphics/Typeface;->create(Ljava/lang/String;I)Landroid/graphics/Typeface;

    .line 288
    .line 289
    .line 290
    move-result-object p1

    .line 291
    iput-object p1, p0, Lj1/f$d;->N:Landroid/graphics/Typeface;
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_2

    .line 292
    .line 293
    goto :goto_3

    .line 294
    :catchall_2
    nop

    .line 295
    sget-object p1, Landroid/graphics/Typeface;->SANS_SERIF:Landroid/graphics/Typeface;

    .line 296
    .line 297
    iput-object p1, p0, Lj1/f$d;->N:Landroid/graphics/Typeface;

    .line 298
    .line 299
    if-nez p1, :cond_2

    .line 300
    .line 301
    sget-object p1, Landroid/graphics/Typeface;->DEFAULT:Landroid/graphics/Typeface;

    .line 302
    .line 303
    iput-object p1, p0, Lj1/f$d;->N:Landroid/graphics/Typeface;

    .line 304
    .line 305
    :cond_2
    :goto_3
    return-void
.end method

.method private c()V
    .locals 2

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-static {v0}, Lk1/c;->b(Z)Lk1/c;

    .line 3
    .line 4
    .line 5
    move-result-object v0

    .line 6
    if-nez v0, :cond_0

    .line 7
    .line 8
    return-void

    .line 9
    :cond_0
    invoke-static {}, Lk1/c;->a()Lk1/c;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    iget-boolean v1, v0, Lk1/c;->a:Z

    .line 14
    .line 15
    if-eqz v1, :cond_1

    .line 16
    .line 17
    sget-object v1, Lj1/p;->b:Lj1/p;

    .line 18
    .line 19
    iput-object v1, p0, Lj1/f$d;->F:Lj1/p;

    .line 20
    .line 21
    :cond_1
    iget v1, v0, Lk1/c;->b:I

    .line 22
    .line 23
    if-eqz v1, :cond_2

    .line 24
    .line 25
    iput v1, p0, Lj1/f$d;->i:I

    .line 26
    .line 27
    :cond_2
    iget v1, v0, Lk1/c;->c:I

    .line 28
    .line 29
    if-eqz v1, :cond_3

    .line 30
    .line 31
    iput v1, p0, Lj1/f$d;->j:I

    .line 32
    .line 33
    :cond_3
    iget-object v1, v0, Lk1/c;->d:Landroid/content/res/ColorStateList;

    .line 34
    .line 35
    if-eqz v1, :cond_4

    .line 36
    .line 37
    iput-object v1, p0, Lj1/f$d;->v:Landroid/content/res/ColorStateList;

    .line 38
    .line 39
    :cond_4
    iget-object v1, v0, Lk1/c;->e:Landroid/content/res/ColorStateList;

    .line 40
    .line 41
    if-eqz v1, :cond_5

    .line 42
    .line 43
    iput-object v1, p0, Lj1/f$d;->x:Landroid/content/res/ColorStateList;

    .line 44
    .line 45
    :cond_5
    iget-object v1, v0, Lk1/c;->f:Landroid/content/res/ColorStateList;

    .line 46
    .line 47
    if-eqz v1, :cond_6

    .line 48
    .line 49
    iput-object v1, p0, Lj1/f$d;->w:Landroid/content/res/ColorStateList;

    .line 50
    .line 51
    :cond_6
    iget v1, v0, Lk1/c;->h:I

    .line 52
    .line 53
    if-eqz v1, :cond_7

    .line 54
    .line 55
    iput v1, p0, Lj1/f$d;->c0:I

    .line 56
    .line 57
    :cond_7
    iget-object v1, v0, Lk1/c;->i:Landroid/graphics/drawable/Drawable;

    .line 58
    .line 59
    if-eqz v1, :cond_8

    .line 60
    .line 61
    iput-object v1, p0, Lj1/f$d;->P:Landroid/graphics/drawable/Drawable;

    .line 62
    .line 63
    :cond_8
    iget v1, v0, Lk1/c;->j:I

    .line 64
    .line 65
    if-eqz v1, :cond_9

    .line 66
    .line 67
    iput v1, p0, Lj1/f$d;->b0:I

    .line 68
    .line 69
    :cond_9
    iget v1, v0, Lk1/c;->k:I

    .line 70
    .line 71
    if-eqz v1, :cond_a

    .line 72
    .line 73
    iput v1, p0, Lj1/f$d;->a0:I

    .line 74
    .line 75
    :cond_a
    iget v1, v0, Lk1/c;->n:I

    .line 76
    .line 77
    if-eqz v1, :cond_b

    .line 78
    .line 79
    iput v1, p0, Lj1/f$d;->F0:I

    .line 80
    .line 81
    :cond_b
    iget v1, v0, Lk1/c;->m:I

    .line 82
    .line 83
    if-eqz v1, :cond_c

    .line 84
    .line 85
    iput v1, p0, Lj1/f$d;->E0:I

    .line 86
    .line 87
    :cond_c
    iget v1, v0, Lk1/c;->o:I

    .line 88
    .line 89
    if-eqz v1, :cond_d

    .line 90
    .line 91
    iput v1, p0, Lj1/f$d;->G0:I

    .line 92
    .line 93
    :cond_d
    iget v1, v0, Lk1/c;->p:I

    .line 94
    .line 95
    if-eqz v1, :cond_e

    .line 96
    .line 97
    iput v1, p0, Lj1/f$d;->H0:I

    .line 98
    .line 99
    :cond_e
    iget v1, v0, Lk1/c;->q:I

    .line 100
    .line 101
    if-eqz v1, :cond_f

    .line 102
    .line 103
    iput v1, p0, Lj1/f$d;->I0:I

    .line 104
    .line 105
    :cond_f
    iget v1, v0, Lk1/c;->g:I

    .line 106
    .line 107
    if-eqz v1, :cond_10

    .line 108
    .line 109
    iput v1, p0, Lj1/f$d;->t:I

    .line 110
    .line 111
    :cond_10
    iget-object v1, v0, Lk1/c;->l:Landroid/content/res/ColorStateList;

    .line 112
    .line 113
    if-eqz v1, :cond_11

    .line 114
    .line 115
    iput-object v1, p0, Lj1/f$d;->y:Landroid/content/res/ColorStateList;

    .line 116
    .line 117
    :cond_11
    iget-object v1, v0, Lk1/c;->r:Lj1/e;

    .line 118
    .line 119
    iput-object v1, p0, Lj1/f$d;->c:Lj1/e;

    .line 120
    .line 121
    iget-object v1, v0, Lk1/c;->s:Lj1/e;

    .line 122
    .line 123
    iput-object v1, p0, Lj1/f$d;->d:Lj1/e;

    .line 124
    .line 125
    iget-object v1, v0, Lk1/c;->t:Lj1/e;

    .line 126
    .line 127
    iput-object v1, p0, Lj1/f$d;->e:Lj1/e;

    .line 128
    .line 129
    iget-object v1, v0, Lk1/c;->u:Lj1/e;

    .line 130
    .line 131
    iput-object v1, p0, Lj1/f$d;->f:Lj1/e;

    .line 132
    .line 133
    iget-object v0, v0, Lk1/c;->v:Lj1/e;

    .line 134
    .line 135
    iput-object v0, p0, Lj1/f$d;->g:Lj1/e;

    .line 136
    .line 137
    return-void
.end method


# virtual methods
.method public a()Lj1/f;
    .locals 1

    .line 1
    new-instance v0, Lj1/f;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Lj1/f;-><init>(Lj1/f$d;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public b(Landroid/content/DialogInterface$OnCancelListener;)Lj1/f$d;
    .locals 0

    .line 1
    iput-object p1, p0, Lj1/f$d;->V:Landroid/content/DialogInterface$OnCancelListener;

    .line 2
    .line 3
    return-object p0
.end method

.method public d(Landroid/content/DialogInterface$OnDismissListener;)Lj1/f$d;
    .locals 0

    .line 1
    iput-object p1, p0, Lj1/f$d;->U:Landroid/content/DialogInterface$OnDismissListener;

    .line 2
    .line 3
    return-object p0
.end method

.method public final e()Landroid/content/Context;
    .locals 1

    .line 1
    iget-object v0, p0, Lj1/f$d;->a:Landroid/content/Context;

    .line 2
    .line 3
    return-object v0
.end method

.method public f(I)Lj1/f$d;
    .locals 1

    .line 1
    if-nez p1, :cond_0

    .line 2
    .line 3
    return-object p0

    .line 4
    :cond_0
    iget-object v0, p0, Lj1/f$d;->a:Landroid/content/Context;

    .line 5
    .line 6
    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    invoke-virtual {p0, p1}, Lj1/f$d;->g(Ljava/lang/CharSequence;)Lj1/f$d;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    return-object p1
.end method

.method public g(Ljava/lang/CharSequence;)Lj1/f$d;
    .locals 0

    .line 1
    iput-object p1, p0, Lj1/f$d;->o:Ljava/lang/CharSequence;

    .line 2
    .line 3
    return-object p0
.end method

.method public h(Lj1/f$g;)Lj1/f$d;
    .locals 0

    .line 1
    iput-object p1, p0, Lj1/f$d;->A:Lj1/f$g;

    .line 2
    .line 3
    return-object p0
.end method

.method public i(ZI)Lj1/f$d;
    .locals 1

    .line 1
    iget-object v0, p0, Lj1/f$d;->s:Landroid/view/View;

    .line 2
    .line 3
    if-nez v0, :cond_1

    .line 4
    .line 5
    if-eqz p1, :cond_0

    .line 6
    .line 7
    const/4 p1, 0x1

    .line 8
    iput-boolean p1, p0, Lj1/f$d;->d0:Z

    .line 9
    .line 10
    const/4 p1, -0x2

    .line 11
    iput p1, p0, Lj1/f$d;->f0:I

    .line 12
    .line 13
    goto :goto_0

    .line 14
    :cond_0
    const/4 p1, 0x0

    .line 15
    iput-boolean p1, p0, Lj1/f$d;->v0:Z

    .line 16
    .line 17
    iput-boolean p1, p0, Lj1/f$d;->d0:Z

    .line 18
    .line 19
    const/4 p1, -0x1

    .line 20
    iput p1, p0, Lj1/f$d;->f0:I

    .line 21
    .line 22
    iput p2, p0, Lj1/f$d;->g0:I

    .line 23
    .line 24
    :goto_0
    return-object p0

    .line 25
    :cond_1
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 26
    .line 27
    const-string p2, "You cannot set progress() when you\'re using a custom view."

    .line 28
    .line 29
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 30
    .line 31
    .line 32
    throw p1
.end method

.method public j(Z)Lj1/f$d;
    .locals 0

    .line 1
    iput-boolean p1, p0, Lj1/f$d;->v0:Z

    .line 2
    .line 3
    return-object p0
.end method

.method public k()Lj1/f;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lj1/f$d;->a()Lj1/f;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lj1/f;->show()V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method

.method public l(I)Lj1/f$d;
    .locals 1

    .line 1
    iget-object v0, p0, Lj1/f$d;->a:Landroid/content/Context;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Landroid/content/Context;->getText(I)Ljava/lang/CharSequence;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-virtual {p0, p1}, Lj1/f$d;->m(Ljava/lang/CharSequence;)Lj1/f$d;

    .line 8
    .line 9
    .line 10
    return-object p0
.end method

.method public m(Ljava/lang/CharSequence;)Lj1/f$d;
    .locals 0

    .line 1
    iput-object p1, p0, Lj1/f$d;->b:Ljava/lang/CharSequence;

    .line 2
    .line 3
    return-object p0
.end method

.method public n(Ljava/lang/String;Ljava/lang/String;)Lj1/f$d;
    .locals 3

    .line 1
    const-string v0, "\""

    .line 2
    .line 3
    const-string v1, "No font asset found for \""

    .line 4
    .line 5
    if-eqz p1, :cond_1

    .line 6
    .line 7
    invoke-virtual {p1}, Ljava/lang/String;->trim()Ljava/lang/String;

    .line 8
    .line 9
    .line 10
    move-result-object v2

    .line 11
    invoke-virtual {v2}, Ljava/lang/String;->isEmpty()Z

    .line 12
    .line 13
    .line 14
    move-result v2

    .line 15
    if-nez v2, :cond_1

    .line 16
    .line 17
    iget-object v2, p0, Lj1/f$d;->a:Landroid/content/Context;

    .line 18
    .line 19
    invoke-static {v2, p1}, Ll1/c;->a(Landroid/content/Context;Ljava/lang/String;)Landroid/graphics/Typeface;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    iput-object v2, p0, Lj1/f$d;->O:Landroid/graphics/Typeface;

    .line 24
    .line 25
    if-eqz v2, :cond_0

    .line 26
    .line 27
    goto :goto_0

    .line 28
    :cond_0
    new-instance p2, Ljava/lang/IllegalArgumentException;

    .line 29
    .line 30
    new-instance v2, Ljava/lang/StringBuilder;

    .line 31
    .line 32
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 33
    .line 34
    .line 35
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 36
    .line 37
    .line 38
    invoke-virtual {v2, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 39
    .line 40
    .line 41
    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 42
    .line 43
    .line 44
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object p1

    .line 48
    invoke-direct {p2, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 49
    .line 50
    .line 51
    throw p2

    .line 52
    :cond_1
    :goto_0
    if-eqz p2, :cond_3

    .line 53
    .line 54
    invoke-virtual {p2}, Ljava/lang/String;->trim()Ljava/lang/String;

    .line 55
    .line 56
    .line 57
    move-result-object p1

    .line 58
    invoke-virtual {p1}, Ljava/lang/String;->isEmpty()Z

    .line 59
    .line 60
    .line 61
    move-result p1

    .line 62
    if-nez p1, :cond_3

    .line 63
    .line 64
    iget-object p1, p0, Lj1/f$d;->a:Landroid/content/Context;

    .line 65
    .line 66
    invoke-static {p1, p2}, Ll1/c;->a(Landroid/content/Context;Ljava/lang/String;)Landroid/graphics/Typeface;

    .line 67
    .line 68
    .line 69
    move-result-object p1

    .line 70
    iput-object p1, p0, Lj1/f$d;->N:Landroid/graphics/Typeface;

    .line 71
    .line 72
    if-eqz p1, :cond_2

    .line 73
    .line 74
    goto :goto_1

    .line 75
    :cond_2
    new-instance p1, Ljava/lang/IllegalArgumentException;

    .line 76
    .line 77
    new-instance v2, Ljava/lang/StringBuilder;

    .line 78
    .line 79
    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    .line 80
    .line 81
    .line 82
    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 83
    .line 84
    .line 85
    invoke-virtual {v2, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 86
    .line 87
    .line 88
    invoke-virtual {v2, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 89
    .line 90
    .line 91
    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 92
    .line 93
    .line 94
    move-result-object p2

    .line 95
    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 96
    .line 97
    .line 98
    throw p1

    .line 99
    :cond_3
    :goto_1
    return-object p0
.end method
