.class final Lcom/google/android/gms/internal/ads/p9;
.super Ljava/lang/Object;
.source "com.google.android.gms:play-services-ads@@21.3.0"

# interfaces
.implements Ljava/lang/Runnable;


# instance fields
.field final synthetic a:I

.field final synthetic b:I

.field final synthetic c:I

.field final synthetic d:F

.field final synthetic e:Lcom/google/android/gms/internal/ads/zzbbk;


# direct methods
.method constructor <init>(Lcom/google/android/gms/internal/ads/zzbbk;IIIF)V
    .locals 0

    .line 1
    iput-object p1, p0, Lcom/google/android/gms/internal/ads/p9;->e:Lcom/google/android/gms/internal/ads/zzbbk;

    .line 2
    .line 3
    iput p2, p0, Lcom/google/android/gms/internal/ads/p9;->a:I

    .line 4
    .line 5
    iput p3, p0, Lcom/google/android/gms/internal/ads/p9;->b:I

    .line 6
    .line 7
    iput p4, p0, Lcom/google/android/gms/internal/ads/p9;->c:I

    .line 8
    .line 9
    iput p5, p0, Lcom/google/android/gms/internal/ads/p9;->d:F

    .line 10
    .line 11
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 12
    .line 13
    .line 14
    return-void
.end method


# virtual methods
.method public final run()V
    .locals 5

    .line 1
    iget-object v0, p0, Lcom/google/android/gms/internal/ads/p9;->e:Lcom/google/android/gms/internal/ads/zzbbk;

    .line 2
    .line 3
    invoke-static {v0}, Lcom/google/android/gms/internal/ads/zzbbk;->zza(Lcom/google/android/gms/internal/ads/zzbbk;)Lcom/google/android/gms/internal/ads/zzbbl;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    iget v1, p0, Lcom/google/android/gms/internal/ads/p9;->a:I

    .line 8
    .line 9
    iget v2, p0, Lcom/google/android/gms/internal/ads/p9;->b:I

    .line 10
    .line 11
    iget v3, p0, Lcom/google/android/gms/internal/ads/p9;->c:I

    .line 12
    .line 13
    iget v4, p0, Lcom/google/android/gms/internal/ads/p9;->d:F

    .line 14
    .line 15
    invoke-interface {v0, v1, v2, v3, v4}, Lcom/google/android/gms/internal/ads/zzbbl;->zzo(IIIF)V

    .line 16
    .line 17
    .line 18
    return-void
.end method
