.class final Lm1/c$a;
.super Ljava/lang/Object;
.source "com.android.billingclient:billing-ktx@@5.0.0"

# interfaces
.implements Lm1/b;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lm1/c;->a(Lcom/android/billingclient/api/a;Lm1/a;Lbb/d;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x18
    name = null
.end annotation


# instance fields
.field final synthetic a:Lub/u;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lub/u<",
            "Lcom/android/billingclient/api/d;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method constructor <init>(Lub/u;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lub/u<",
            "Lcom/android/billingclient/api/d;",
            ">;)V"
        }
    .end annotation

    .line 1
    iput-object p1, p0, Lm1/c$a;->a:Lub/u;

    .line 2
    .line 3
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(Lcom/android/billingclient/api/d;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lm1/c$a;->a:Lub/u;

    .line 2
    .line 3
    const-string v1, "it"

    .line 4
    .line 5
    invoke-static {p1, v1}, Lkb/l;->g(Ljava/lang/Object;Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    invoke-interface {v0, p1}, Lub/u;->i0(Ljava/lang/Object;)Z

    .line 9
    .line 10
    .line 11
    return-void
.end method
