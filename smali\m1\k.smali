.class public interface abstract Lm1/k;
.super Ljava/lang/Object;
.source "com.android.billingclient:billing@@5.0.0"


# virtual methods
.method public abstract a(Lcom/android/billingclient/api/d;Ljava/util/List;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lcom/android/billingclient/api/d;",
            "Ljava/util/List<",
            "Lcom/android/billingclient/api/Purchase;",
            ">;)V"
        }
    .end annotation
.end method
