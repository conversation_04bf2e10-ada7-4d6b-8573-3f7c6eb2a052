.class public final Lf3/v;
.super Ljava/lang/Object;
.source "TransportRuntime_Factory.java"

# interfaces
.implements Li3/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Li3/b<",
        "Lf3/t;",
        ">;"
    }
.end annotation


# instance fields
.field private final a:Lva/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lva/a<",
            "Lq3/a;",
            ">;"
        }
    .end annotation
.end field

.field private final b:Lva/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lva/a<",
            "Lq3/a;",
            ">;"
        }
    .end annotation
.end field

.field private final c:Lva/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lva/a<",
            "Lm3/e;",
            ">;"
        }
    .end annotation
.end field

.field private final d:Lva/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lva/a<",
            "Ln3/r;",
            ">;"
        }
    .end annotation
.end field

.field private final e:Lva/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lva/a<",
            "Ln3/v;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lva/a;Lva/a;Lva/a;Lva/a;Lva/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lva/a<",
            "Lq3/a;",
            ">;",
            "Lva/a<",
            "Lq3/a;",
            ">;",
            "Lva/a<",
            "Lm3/e;",
            ">;",
            "Lva/a<",
            "Ln3/r;",
            ">;",
            "Lva/a<",
            "Ln3/v;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lf3/v;->a:Lva/a;

    .line 5
    .line 6
    iput-object p2, p0, Lf3/v;->b:Lva/a;

    .line 7
    .line 8
    iput-object p3, p0, Lf3/v;->c:Lva/a;

    .line 9
    .line 10
    iput-object p4, p0, Lf3/v;->d:Lva/a;

    .line 11
    .line 12
    iput-object p5, p0, Lf3/v;->e:Lva/a;

    .line 13
    .line 14
    return-void
.end method

.method public static a(Lva/a;Lva/a;Lva/a;Lva/a;Lva/a;)Lf3/v;
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lva/a<",
            "Lq3/a;",
            ">;",
            "Lva/a<",
            "Lq3/a;",
            ">;",
            "Lva/a<",
            "Lm3/e;",
            ">;",
            "Lva/a<",
            "Ln3/r;",
            ">;",
            "Lva/a<",
            "Ln3/v;",
            ">;)",
            "Lf3/v;"
        }
    .end annotation

    .line 1
    new-instance v6, Lf3/v;

    .line 2
    .line 3
    move-object v0, v6

    .line 4
    move-object v1, p0

    .line 5
    move-object v2, p1

    .line 6
    move-object v3, p2

    .line 7
    move-object v4, p3

    .line 8
    move-object v5, p4

    .line 9
    invoke-direct/range {v0 .. v5}, Lf3/v;-><init>(Lva/a;Lva/a;Lva/a;Lva/a;Lva/a;)V

    .line 10
    .line 11
    .line 12
    return-object v6
.end method

.method public static c(Lq3/a;Lq3/a;Lm3/e;Ln3/r;Ln3/v;)Lf3/t;
    .locals 7

    .line 1
    new-instance v6, Lf3/t;

    .line 2
    .line 3
    move-object v0, v6

    .line 4
    move-object v1, p0

    .line 5
    move-object v2, p1

    .line 6
    move-object v3, p2

    .line 7
    move-object v4, p3

    .line 8
    move-object v5, p4

    .line 9
    invoke-direct/range {v0 .. v5}, Lf3/t;-><init>(Lq3/a;Lq3/a;Lm3/e;Ln3/r;Ln3/v;)V

    .line 10
    .line 11
    .line 12
    return-object v6
.end method


# virtual methods
.method public b()Lf3/t;
    .locals 5

    .line 1
    iget-object v0, p0, Lf3/v;->a:Lva/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lva/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Lq3/a;

    .line 8
    .line 9
    iget-object v1, p0, Lf3/v;->b:Lva/a;

    .line 10
    .line 11
    invoke-interface {v1}, Lva/a;->get()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    check-cast v1, Lq3/a;

    .line 16
    .line 17
    iget-object v2, p0, Lf3/v;->c:Lva/a;

    .line 18
    .line 19
    invoke-interface {v2}, Lva/a;->get()Ljava/lang/Object;

    .line 20
    .line 21
    .line 22
    move-result-object v2

    .line 23
    check-cast v2, Lm3/e;

    .line 24
    .line 25
    iget-object v3, p0, Lf3/v;->d:Lva/a;

    .line 26
    .line 27
    invoke-interface {v3}, Lva/a;->get()Ljava/lang/Object;

    .line 28
    .line 29
    .line 30
    move-result-object v3

    .line 31
    check-cast v3, Ln3/r;

    .line 32
    .line 33
    iget-object v4, p0, Lf3/v;->e:Lva/a;

    .line 34
    .line 35
    invoke-interface {v4}, Lva/a;->get()Ljava/lang/Object;

    .line 36
    .line 37
    .line 38
    move-result-object v4

    .line 39
    check-cast v4, Ln3/v;

    .line 40
    .line 41
    invoke-static {v0, v1, v2, v3, v4}, Lf3/v;->c(Lq3/a;Lq3/a;Lm3/e;Ln3/r;Ln3/v;)Lf3/t;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lf3/v;->b()Lf3/t;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
