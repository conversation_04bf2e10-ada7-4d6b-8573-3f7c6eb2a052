.class public final Lg3/l;
.super Ljava/lang/Object;
.source "MetadataBackendRegistry_Factory.java"

# interfaces
.implements Li3/b;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "Ljava/lang/Object;",
        "Li3/b<",
        "Lg3/k;",
        ">;"
    }
.end annotation


# instance fields
.field private final a:Lva/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lva/a<",
            "Landroid/content/Context;",
            ">;"
        }
    .end annotation
.end field

.field private final b:Lva/a;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Lva/a<",
            "Lg3/i;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lva/a;Lva/a;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lva/a<",
            "Landroid/content/Context;",
            ">;",
            "Lva/a<",
            "Lg3/i;",
            ">;)V"
        }
    .end annotation

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lg3/l;->a:Lva/a;

    .line 5
    .line 6
    iput-object p2, p0, Lg3/l;->b:Lva/a;

    .line 7
    .line 8
    return-void
.end method

.method public static a(Lva/a;Lva/a;)Lg3/l;
    .locals 1
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Lva/a<",
            "Landroid/content/Context;",
            ">;",
            "Lva/a<",
            "Lg3/i;",
            ">;)",
            "Lg3/l;"
        }
    .end annotation

    .line 1
    new-instance v0, Lg3/l;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1}, Lg3/l;-><init>(Lva/a;Lva/a;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public static c(Landroid/content/Context;Ljava/lang/Object;)Lg3/k;
    .locals 1

    .line 1
    new-instance v0, Lg3/k;

    .line 2
    .line 3
    check-cast p1, Lg3/i;

    .line 4
    .line 5
    invoke-direct {v0, p0, p1}, Lg3/k;-><init>(Landroid/content/Context;Lg3/i;)V

    .line 6
    .line 7
    .line 8
    return-object v0
.end method


# virtual methods
.method public b()Lg3/k;
    .locals 2

    .line 1
    iget-object v0, p0, Lg3/l;->a:Lva/a;

    .line 2
    .line 3
    invoke-interface {v0}, Lva/a;->get()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    check-cast v0, Landroid/content/Context;

    .line 8
    .line 9
    iget-object v1, p0, Lg3/l;->b:Lva/a;

    .line 10
    .line 11
    invoke-interface {v1}, Lva/a;->get()Ljava/lang/Object;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-static {v0, v1}, Lg3/l;->c(Landroid/content/Context;Ljava/lang/Object;)Lg3/k;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    return-object v0
.end method

.method public bridge synthetic get()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lg3/l;->b()Lg3/k;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
