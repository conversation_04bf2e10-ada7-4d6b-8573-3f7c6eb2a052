.class public final Li2/b;
.super Ljava/lang/Object;
.source "ErrorRequestCoordinator.java"

# interfaces
.implements Li2/d;
.implements Li2/c;


# instance fields
.field private final a:Ljava/lang/Object;

.field private final b:Li2/d;

.field private volatile c:Li2/c;

.field private volatile d:Li2/c;

.field private e:Li2/d$a;

.field private f:Li2/d$a;


# direct methods
.method public constructor <init>(Ljava/lang/Object;Li2/d;)V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    sget-object v0, Li2/d$a;->d:Li2/d$a;

    .line 5
    .line 6
    iput-object v0, p0, Li2/b;->e:Li2/d$a;

    .line 7
    .line 8
    iput-object v0, p0, Li2/b;->f:Li2/d$a;

    .line 9
    .line 10
    iput-object p1, p0, Li2/b;->a:Ljava/lang/Object;

    .line 11
    .line 12
    iput-object p2, p0, Li2/b;->b:Li2/d;

    .line 13
    .line 14
    return-void
.end method

.method private k(Li2/c;)Z
    .locals 2

    .line 1
    iget-object v0, p0, Li2/b;->c:Li2/c;

    .line 2
    .line 3
    invoke-virtual {p1, v0}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-nez v0, :cond_1

    .line 8
    .line 9
    iget-object v0, p0, Li2/b;->e:Li2/d$a;

    .line 10
    .line 11
    sget-object v1, Li2/d$a;->f:Li2/d$a;

    .line 12
    .line 13
    if-ne v0, v1, :cond_0

    .line 14
    .line 15
    iget-object v0, p0, Li2/b;->d:Li2/c;

    .line 16
    .line 17
    invoke-virtual {p1, v0}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    move-result p1

    .line 21
    if-eqz p1, :cond_0

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_0
    const/4 p1, 0x0

    .line 25
    goto :goto_1

    .line 26
    :cond_1
    :goto_0
    const/4 p1, 0x1

    .line 27
    :goto_1
    return p1
.end method

.method private l()Z
    .locals 1

    .line 1
    iget-object v0, p0, Li2/b;->b:Li2/d;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    invoke-interface {v0, p0}, Li2/d;->g(Li2/c;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    goto :goto_1

    .line 14
    :cond_1
    :goto_0
    const/4 v0, 0x1

    .line 15
    :goto_1
    return v0
.end method

.method private m()Z
    .locals 1

    .line 1
    iget-object v0, p0, Li2/b;->b:Li2/d;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    invoke-interface {v0, p0}, Li2/d;->e(Li2/c;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    goto :goto_1

    .line 14
    :cond_1
    :goto_0
    const/4 v0, 0x1

    .line 15
    :goto_1
    return v0
.end method

.method private n()Z
    .locals 1

    .line 1
    iget-object v0, p0, Li2/b;->b:Li2/d;

    .line 2
    .line 3
    if-eqz v0, :cond_1

    .line 4
    .line 5
    invoke-interface {v0, p0}, Li2/d;->i(Li2/c;)Z

    .line 6
    .line 7
    .line 8
    move-result v0

    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    const/4 v0, 0x0

    .line 13
    goto :goto_1

    .line 14
    :cond_1
    :goto_0
    const/4 v0, 0x1

    .line 15
    :goto_1
    return v0
.end method


# virtual methods
.method public a(Li2/c;)V
    .locals 2

    .line 1
    iget-object v0, p0, Li2/b;->a:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    iget-object v1, p0, Li2/b;->d:Li2/c;

    .line 5
    .line 6
    invoke-virtual {p1, v1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    .line 7
    .line 8
    .line 9
    move-result p1

    .line 10
    if-nez p1, :cond_1

    .line 11
    .line 12
    sget-object p1, Li2/d$a;->f:Li2/d$a;

    .line 13
    .line 14
    iput-object p1, p0, Li2/b;->e:Li2/d$a;

    .line 15
    .line 16
    iget-object p1, p0, Li2/b;->f:Li2/d$a;

    .line 17
    .line 18
    sget-object v1, Li2/d$a;->b:Li2/d$a;

    .line 19
    .line 20
    if-eq p1, v1, :cond_0

    .line 21
    .line 22
    iput-object v1, p0, Li2/b;->f:Li2/d$a;

    .line 23
    .line 24
    iget-object p1, p0, Li2/b;->d:Li2/c;

    .line 25
    .line 26
    invoke-interface {p1}, Li2/c;->h()V

    .line 27
    .line 28
    .line 29
    :cond_0
    monitor-exit v0

    .line 30
    return-void

    .line 31
    :cond_1
    sget-object p1, Li2/d$a;->f:Li2/d$a;

    .line 32
    .line 33
    iput-object p1, p0, Li2/b;->f:Li2/d$a;

    .line 34
    .line 35
    iget-object p1, p0, Li2/b;->b:Li2/d;

    .line 36
    .line 37
    if-eqz p1, :cond_2

    .line 38
    .line 39
    invoke-interface {p1, p0}, Li2/d;->a(Li2/c;)V

    .line 40
    .line 41
    .line 42
    :cond_2
    monitor-exit v0

    .line 43
    return-void

    .line 44
    :catchall_0
    move-exception p1

    .line 45
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 46
    throw p1
.end method

.method public b(Li2/c;)V
    .locals 2

    .line 1
    iget-object v0, p0, Li2/b;->a:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    iget-object v1, p0, Li2/b;->c:Li2/c;

    .line 5
    .line 6
    invoke-virtual {p1, v1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    .line 7
    .line 8
    .line 9
    move-result v1

    .line 10
    if-eqz v1, :cond_0

    .line 11
    .line 12
    sget-object p1, Li2/d$a;->e:Li2/d$a;

    .line 13
    .line 14
    iput-object p1, p0, Li2/b;->e:Li2/d$a;

    .line 15
    .line 16
    goto :goto_0

    .line 17
    :cond_0
    iget-object v1, p0, Li2/b;->d:Li2/c;

    .line 18
    .line 19
    invoke-virtual {p1, v1}, Ljava/lang/Object;->equals(Ljava/lang/Object;)Z

    .line 20
    .line 21
    .line 22
    move-result p1

    .line 23
    if-eqz p1, :cond_1

    .line 24
    .line 25
    sget-object p1, Li2/d$a;->e:Li2/d$a;

    .line 26
    .line 27
    iput-object p1, p0, Li2/b;->f:Li2/d$a;

    .line 28
    .line 29
    :cond_1
    :goto_0
    iget-object p1, p0, Li2/b;->b:Li2/d;

    .line 30
    .line 31
    if-eqz p1, :cond_2

    .line 32
    .line 33
    invoke-interface {p1, p0}, Li2/d;->b(Li2/c;)V

    .line 34
    .line 35
    .line 36
    :cond_2
    monitor-exit v0

    .line 37
    return-void

    .line 38
    :catchall_0
    move-exception p1

    .line 39
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 40
    throw p1
.end method

.method public c()Z
    .locals 2

    .line 1
    iget-object v0, p0, Li2/b;->a:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    iget-object v1, p0, Li2/b;->c:Li2/c;

    .line 5
    .line 6
    invoke-interface {v1}, Li2/c;->c()Z

    .line 7
    .line 8
    .line 9
    move-result v1

    .line 10
    if-nez v1, :cond_1

    .line 11
    .line 12
    iget-object v1, p0, Li2/b;->d:Li2/c;

    .line 13
    .line 14
    invoke-interface {v1}, Li2/c;->c()Z

    .line 15
    .line 16
    .line 17
    move-result v1

    .line 18
    if-eqz v1, :cond_0

    .line 19
    .line 20
    goto :goto_0

    .line 21
    :cond_0
    const/4 v1, 0x0

    .line 22
    goto :goto_1

    .line 23
    :cond_1
    :goto_0
    const/4 v1, 0x1

    .line 24
    :goto_1
    monitor-exit v0

    .line 25
    return v1

    .line 26
    :catchall_0
    move-exception v1

    .line 27
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 28
    throw v1
.end method

.method public clear()V
    .locals 3

    .line 1
    iget-object v0, p0, Li2/b;->a:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    sget-object v1, Li2/d$a;->d:Li2/d$a;

    .line 5
    .line 6
    iput-object v1, p0, Li2/b;->e:Li2/d$a;

    .line 7
    .line 8
    iget-object v2, p0, Li2/b;->c:Li2/c;

    .line 9
    .line 10
    invoke-interface {v2}, Li2/c;->clear()V

    .line 11
    .line 12
    .line 13
    iget-object v2, p0, Li2/b;->f:Li2/d$a;

    .line 14
    .line 15
    if-eq v2, v1, :cond_0

    .line 16
    .line 17
    iput-object v1, p0, Li2/b;->f:Li2/d$a;

    .line 18
    .line 19
    iget-object v1, p0, Li2/b;->d:Li2/c;

    .line 20
    .line 21
    invoke-interface {v1}, Li2/c;->clear()V

    .line 22
    .line 23
    .line 24
    :cond_0
    monitor-exit v0

    .line 25
    return-void

    .line 26
    :catchall_0
    move-exception v1

    .line 27
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 28
    throw v1
.end method

.method public d(Li2/c;)Z
    .locals 3

    .line 1
    instance-of v0, p1, Li2/b;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    if-eqz v0, :cond_0

    .line 5
    .line 6
    check-cast p1, Li2/b;

    .line 7
    .line 8
    iget-object v0, p0, Li2/b;->c:Li2/c;

    .line 9
    .line 10
    iget-object v2, p1, Li2/b;->c:Li2/c;

    .line 11
    .line 12
    invoke-interface {v0, v2}, Li2/c;->d(Li2/c;)Z

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    if-eqz v0, :cond_0

    .line 17
    .line 18
    iget-object v0, p0, Li2/b;->d:Li2/c;

    .line 19
    .line 20
    iget-object p1, p1, Li2/b;->d:Li2/c;

    .line 21
    .line 22
    invoke-interface {v0, p1}, Li2/c;->d(Li2/c;)Z

    .line 23
    .line 24
    .line 25
    move-result p1

    .line 26
    if-eqz p1, :cond_0

    .line 27
    .line 28
    const/4 v1, 0x1

    .line 29
    :cond_0
    return v1
.end method

.method public e(Li2/c;)Z
    .locals 2

    .line 1
    iget-object v0, p0, Li2/b;->a:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    invoke-direct {p0}, Li2/b;->m()Z

    .line 5
    .line 6
    .line 7
    move-result v1

    .line 8
    if-eqz v1, :cond_0

    .line 9
    .line 10
    invoke-direct {p0, p1}, Li2/b;->k(Li2/c;)Z

    .line 11
    .line 12
    .line 13
    move-result p1

    .line 14
    if-eqz p1, :cond_0

    .line 15
    .line 16
    const/4 p1, 0x1

    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 p1, 0x0

    .line 19
    :goto_0
    monitor-exit v0

    .line 20
    return p1

    .line 21
    :catchall_0
    move-exception p1

    .line 22
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 23
    throw p1
.end method

.method public f()Z
    .locals 3

    .line 1
    iget-object v0, p0, Li2/b;->a:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    iget-object v1, p0, Li2/b;->e:Li2/d$a;

    .line 5
    .line 6
    sget-object v2, Li2/d$a;->d:Li2/d$a;

    .line 7
    .line 8
    if-ne v1, v2, :cond_0

    .line 9
    .line 10
    iget-object v1, p0, Li2/b;->f:Li2/d$a;

    .line 11
    .line 12
    if-ne v1, v2, :cond_0

    .line 13
    .line 14
    const/4 v1, 0x1

    .line 15
    goto :goto_0

    .line 16
    :cond_0
    const/4 v1, 0x0

    .line 17
    :goto_0
    monitor-exit v0

    .line 18
    return v1

    .line 19
    :catchall_0
    move-exception v1

    .line 20
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 21
    throw v1
.end method

.method public g(Li2/c;)Z
    .locals 2

    .line 1
    iget-object v0, p0, Li2/b;->a:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    invoke-direct {p0}, Li2/b;->l()Z

    .line 5
    .line 6
    .line 7
    move-result v1

    .line 8
    if-eqz v1, :cond_0

    .line 9
    .line 10
    invoke-direct {p0, p1}, Li2/b;->k(Li2/c;)Z

    .line 11
    .line 12
    .line 13
    move-result p1

    .line 14
    if-eqz p1, :cond_0

    .line 15
    .line 16
    const/4 p1, 0x1

    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 p1, 0x0

    .line 19
    :goto_0
    monitor-exit v0

    .line 20
    return p1

    .line 21
    :catchall_0
    move-exception p1

    .line 22
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 23
    throw p1
.end method

.method public getRoot()Li2/d;
    .locals 2

    .line 1
    iget-object v0, p0, Li2/b;->a:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    iget-object v1, p0, Li2/b;->b:Li2/d;

    .line 5
    .line 6
    if-eqz v1, :cond_0

    .line 7
    .line 8
    invoke-interface {v1}, Li2/d;->getRoot()Li2/d;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    goto :goto_0

    .line 13
    :cond_0
    move-object v1, p0

    .line 14
    :goto_0
    monitor-exit v0

    .line 15
    return-object v1

    .line 16
    :catchall_0
    move-exception v1

    .line 17
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 18
    throw v1
.end method

.method public h()V
    .locals 3

    .line 1
    iget-object v0, p0, Li2/b;->a:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    iget-object v1, p0, Li2/b;->e:Li2/d$a;

    .line 5
    .line 6
    sget-object v2, Li2/d$a;->b:Li2/d$a;

    .line 7
    .line 8
    if-eq v1, v2, :cond_0

    .line 9
    .line 10
    iput-object v2, p0, Li2/b;->e:Li2/d$a;

    .line 11
    .line 12
    iget-object v1, p0, Li2/b;->c:Li2/c;

    .line 13
    .line 14
    invoke-interface {v1}, Li2/c;->h()V

    .line 15
    .line 16
    .line 17
    :cond_0
    monitor-exit v0

    .line 18
    return-void

    .line 19
    :catchall_0
    move-exception v1

    .line 20
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 21
    throw v1
.end method

.method public i(Li2/c;)Z
    .locals 2

    .line 1
    iget-object v0, p0, Li2/b;->a:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    invoke-direct {p0}, Li2/b;->n()Z

    .line 5
    .line 6
    .line 7
    move-result v1

    .line 8
    if-eqz v1, :cond_0

    .line 9
    .line 10
    invoke-direct {p0, p1}, Li2/b;->k(Li2/c;)Z

    .line 11
    .line 12
    .line 13
    move-result p1

    .line 14
    if-eqz p1, :cond_0

    .line 15
    .line 16
    const/4 p1, 0x1

    .line 17
    goto :goto_0

    .line 18
    :cond_0
    const/4 p1, 0x0

    .line 19
    :goto_0
    monitor-exit v0

    .line 20
    return p1

    .line 21
    :catchall_0
    move-exception p1

    .line 22
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 23
    throw p1
.end method

.method public isRunning()Z
    .locals 3

    .line 1
    iget-object v0, p0, Li2/b;->a:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    iget-object v1, p0, Li2/b;->e:Li2/d$a;

    .line 5
    .line 6
    sget-object v2, Li2/d$a;->b:Li2/d$a;

    .line 7
    .line 8
    if-eq v1, v2, :cond_1

    .line 9
    .line 10
    iget-object v1, p0, Li2/b;->f:Li2/d$a;

    .line 11
    .line 12
    if-ne v1, v2, :cond_0

    .line 13
    .line 14
    goto :goto_0

    .line 15
    :cond_0
    const/4 v1, 0x0

    .line 16
    goto :goto_1

    .line 17
    :cond_1
    :goto_0
    const/4 v1, 0x1

    .line 18
    :goto_1
    monitor-exit v0

    .line 19
    return v1

    .line 20
    :catchall_0
    move-exception v1

    .line 21
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 22
    throw v1
.end method

.method public j()Z
    .locals 3

    .line 1
    iget-object v0, p0, Li2/b;->a:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    iget-object v1, p0, Li2/b;->e:Li2/d$a;

    .line 5
    .line 6
    sget-object v2, Li2/d$a;->e:Li2/d$a;

    .line 7
    .line 8
    if-eq v1, v2, :cond_1

    .line 9
    .line 10
    iget-object v1, p0, Li2/b;->f:Li2/d$a;

    .line 11
    .line 12
    if-ne v1, v2, :cond_0

    .line 13
    .line 14
    goto :goto_0

    .line 15
    :cond_0
    const/4 v1, 0x0

    .line 16
    goto :goto_1

    .line 17
    :cond_1
    :goto_0
    const/4 v1, 0x1

    .line 18
    :goto_1
    monitor-exit v0

    .line 19
    return v1

    .line 20
    :catchall_0
    move-exception v1

    .line 21
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 22
    throw v1
.end method

.method public o(Li2/c;Li2/c;)V
    .locals 0

    .line 1
    iput-object p1, p0, Li2/b;->c:Li2/c;

    .line 2
    .line 3
    iput-object p2, p0, Li2/b;->d:Li2/c;

    .line 4
    .line 5
    return-void
.end method

.method public pause()V
    .locals 3

    .line 1
    iget-object v0, p0, Li2/b;->a:Ljava/lang/Object;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    iget-object v1, p0, Li2/b;->e:Li2/d$a;

    .line 5
    .line 6
    sget-object v2, Li2/d$a;->b:Li2/d$a;

    .line 7
    .line 8
    if-ne v1, v2, :cond_0

    .line 9
    .line 10
    sget-object v1, Li2/d$a;->c:Li2/d$a;

    .line 11
    .line 12
    iput-object v1, p0, Li2/b;->e:Li2/d$a;

    .line 13
    .line 14
    iget-object v1, p0, Li2/b;->c:Li2/c;

    .line 15
    .line 16
    invoke-interface {v1}, Li2/c;->pause()V

    .line 17
    .line 18
    .line 19
    :cond_0
    iget-object v1, p0, Li2/b;->f:Li2/d$a;

    .line 20
    .line 21
    if-ne v1, v2, :cond_1

    .line 22
    .line 23
    sget-object v1, Li2/d$a;->c:Li2/d$a;

    .line 24
    .line 25
    iput-object v1, p0, Li2/b;->f:Li2/d$a;

    .line 26
    .line 27
    iget-object v1, p0, Li2/b;->d:Li2/c;

    .line 28
    .line 29
    invoke-interface {v1}, Li2/c;->pause()V

    .line 30
    .line 31
    .line 32
    :cond_1
    monitor-exit v0

    .line 33
    return-void

    .line 34
    :catchall_0
    move-exception v1

    .line 35
    monitor-exit v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 36
    throw v1
.end method
