.class public interface abstract Li2/d;
.super Ljava/lang/Object;
.source "RequestCoordinator.java"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Li2/d$a;
    }
.end annotation


# virtual methods
.method public abstract a(Li2/c;)V
.end method

.method public abstract b(Li2/c;)V
.end method

.method public abstract c()Z
.end method

.method public abstract e(Li2/c;)Z
.end method

.method public abstract g(Li2/c;)Z
.end method

.method public abstract getRoot()Li2/d;
.end method

.method public abstract i(Li2/c;)Z
.end method
