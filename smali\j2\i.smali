.class public interface abstract Lj2/i;
.super Ljava/lang/Object;
.source "Target.java"

# interfaces
.implements Lf2/i;


# annotations
.annotation system Ldalvik/annotation/Signature;
    value = {
        "<R:",
        "Ljava/lang/Object;",
        ">",
        "Ljava/lang/Object;",
        "Lf2/i;"
    }
.end annotation


# virtual methods
.method public abstract a(Li2/c;)V
.end method

.method public abstract b(Ljava/lang/Object;Lk2/d;)V
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(TR;",
            "Lk2/d<",
            "-TR;>;)V"
        }
    .end annotation
.end method

.method public abstract c(Lj2/h;)V
.end method

.method public abstract e(Landroid/graphics/drawable/Drawable;)V
.end method

.method public abstract f(Lj2/h;)V
.end method

.method public abstract h(Landroid/graphics/drawable/Drawable;)V
.end method

.method public abstract i()Li2/c;
.end method

.method public abstract j(Landroid/graphics/drawable/Drawable;)V
.end method
